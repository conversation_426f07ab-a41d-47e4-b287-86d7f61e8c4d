#
# A fatal error has been detected by the Java Runtime Environment:
#
#  SIGSEGV (0xb) at pc=0x00000001736dc9a8, pid=45528, tid=0x0000000000001303
#
# JRE version: Java(TM) SE Runtime Environment (8.0_401) (build 1.8.0_401-b10)
# Java VM: Java HotSpot(TM) 64-Bit Server VM (25.401-b10 mixed mode bsd-aarch64 compressed oops)
# Problematic frame:
# C  [libtesseract.5.dylib+0x289a8]  tesseract::Tesseract::recog_all_words(tesseract::PAGE_RES*, tesseract::ETEXT_DESC*, tesseract::TBOX const*, char const*, int)+0xd0
#
# Failed to write core dump. Core dumps have been disabled. To enable core dumping, try "ulimit -c unlimited" before starting Java again
#
# If you would like to submit a bug report, please visit:
#   http://bugreport.java.com/bugreport/crash.jsp
# The crash happened outside the Java Virtual Machine in native code.
# See problematic frame for where to report the bug.
#

---------------  T H R E A D  ---------------

Current thread (0x000000011f009800):  JavaThread "main" [_thread_in_native, id=4867, stack(0x000000016d2dc000,0x000000016d4df000)]

siginfo: si_signo: 11 (SIGSEGV), si_code: 2 (SEGV_ACCERR), si_addr: 0x000000000000000c

Registers:
 x0=0x000000012006fe50  x1=0x0000000000000000  x2=0x0000000000000000  x3=0x0000000000000000
 x4=0x0000000000000000  x5=0x0000000000000000  x6=0x000000000000000c  x7=0x0000000000000000
 x8=0x0000000000000000  x9=0x0000000000000000 x10=0x0000600001e3a520 x11=0x0000600001e3a9a0
x12=0x000000016d4dda68 x13=0x00000000ffffffbb x14=0x00000000000007fb x15=0x00000000b0a227fb
x16=0x0000000186336a90 x17=0x00000001e5c63cf0 x18=0x0000000029ac2bd5 x19=0x000000011016b102
x20=0x00006000033023f0 x21=0x0000000110148000 x22=0x0000000000000000 x23=0x0000000000000000
x24=0x0000000000000000 x25=0x000000016d4ddd70 x26=0x0000000110169b7c x27=0x000000011016ca00
x28=0x000000011016ba64  fp=0x000000016d4ddb60  lr=0x00000001736dc988  sp=0x000000016d4dd990
pc=0x00000001736dc9a8 cpsr=0x0000000080001000
Top of Stack: (sp=0x000000016d4dd990)
0x000000016d4dd990:   010d00890102005b 010d009d00000000
0x000000016d4dd9a0:   0000000000000000 0000600003467600
0x000000016d4dd9b0:   00006000034675a0 0000600003467600
0x000000016d4dd9c0:   00006000033023f0 0000000000000000
0x000000016d4dd9d0:   0000000000000000 0000000000000000
0x000000016d4dd9e0:   000000012006fe50 0000600001e3a5b0
0x000000016d4dd9f0:   0000600001e3a520 00000001200706e0
0x000000016d4dda00:   0000600001e3a9a0 0000600001e3a520
0x000000016d4dda10:   00006000033023f8 0000600001e3a520
0x000000016d4dda20:   0000600001e3a520 0000600001e3a520
0x000000016d4dda30:   0000600001e3a520 0000600001000000
0x000000016d4dda40:   0000600001e3a548 0000600001e3a5b0
0x000000016d4dda50:   0000600001e3a9a0 0000600001e3aa00
0x000000016d4dda60:   0000600001e3a5b0 0000600003010000
0x000000016d4dda70:   0000600001e3a9c0 00000001200706e0
0x000000016d4dda80:   0000000120070030 00000001200706e0
0x000000016d4dda90:   00000001200706e0 0000000000010000
0x000000016d4ddaa0:   0000600001e3a5d0 000000012006eae0
0x000000016d4ddab0:   000000012006fe50 000000012006eae0
0x000000016d4ddac0:   000000012006fe50 0000000173000000
0x000000016d4ddad0:   0000600001e3a9c0 0000000120070030
0x000000016d4ddae0:   00000001200706e0 0000000120070030
0x000000016d4ddaf0:   00000001200706e0 0000600002000000
0x000000016d4ddb00:   0000000000000000 0000000110169cdc
0x000000016d4ddb10:   000000016d4dddb0 0000000000000001
0x000000016d4ddb20:   0000000000000001 000000016d4ddd70
0x000000016d4ddb30:   0000000000000000 0000000110169cdc
0x000000016d4ddb40:   0000000110148000 00006000033023f0
0x000000016d4ddb50:   0000000000000000 0000600002b7cb60
0x000000016d4ddb60:   000000016d4ddbc0 00000001736b9168
0x000000016d4ddb70:   0000000000000001 0000000715596101
0x000000016d4ddb80:   000000071b942e88 0000001600000000 

Instructions: (pc=0x00000001736dc9a8)
0x00000001736dc8a8:   c8 00 f8 36 e0 07 40 f9 36 fe 04 94 03 00 00 14
0x00000001736dc8b8:   01 00 00 14 f4 03 00 aa 68 5e c0 39 68 00 f8 36
0x00000001736dc8c8:   60 02 40 f9 2f fe 04 94 e0 03 14 aa 10 fd 04 94
0x00000001736dc8d8:   ff 83 07 d1 fc 6f 18 a9 fa 67 19 a9 f8 5f 1a a9
0x00000001736dc8e8:   f6 57 1b a9 f4 4f 1c a9 fd 7b 1d a9 fd 43 07 91
0x00000001736dc8f8:   f7 03 05 aa e4 0b 00 f9 f6 03 03 aa f3 03 02 aa
0x00000001736dc908:   f4 03 01 aa f5 03 00 aa ff 5b 00 f9 ff 73 00 f9
0x00000001736dc918:   08 90 40 91 1b 01 28 91 08 8c 40 91 1c 91 29 91
0x00000001736dc928:   08 8c 40 91 18 09 04 91 ff 8b 00 f9 ff a3 00 f9
0x00000001736dc938:   08 84 40 91 ff 43 00 f9 e1 1b 00 f9 1a f1 2d 91
0x00000001736dc948:   e0 c3 00 91 01 00 80 52 ca 55 01 94 48 3b 66 39
0x00000001736dc958:   88 00 00 34 28 00 80 52 48 bb 26 39 08 83 0c 39
0x00000001736dc968:   ff 06 00 71 e8 0f 00 54 f7 0b 00 b9 f7 03 13 aa
0x00000001736dc978:   f3 03 18 aa e0 c3 00 91 01 00 80 52 bd 55 01 94
0x00000001736dc988:   a8 c2 54 b9 f8 03 16 aa 1f 05 00 71 8b 00 00 54
0x00000001736dc998:   e0 03 15 aa 07 aa 01 94 06 00 00 14 a8 e2 44 f9
0x00000001736dc9a8:   08 31 40 39 68 00 00 34 e0 03 15 aa 21 aa 01 94
0x00000001736dc9b8:   e8 03 1b aa 79 5b 40 a9 3f 03 16 eb a0 01 00 54
0x00000001736dc9c8:   20 03 40 f9 08 c0 54 b9 1f 05 00 71 6b 00 00 54
0x00000001736dc9d8:   f8 a9 01 94 05 00 00 14 08 e0 44 f9 08 31 40 39
0x00000001736dc9e8:   48 00 00 34 13 aa 01 94 39 23 00 91 f3 ff ff 17
0x00000001736dc9f8:   ff ff 01 a9 ff 17 00 f9 e5 63 00 91 e0 03 15 aa
0x00000001736dca08:   21 00 80 52 f6 03 18 aa e2 03 18 aa e3 0b 40 f9
0x00000001736dca18:   e4 03 14 aa 77 fc ff 97 f8 03 13 aa f3 03 17 aa
0x00000001736dca28:   f7 0b 40 b9 88 03 40 b9 88 00 00 34 e1 63 00 91
0x00000001736dca38:   e0 03 15 aa 52 d6 00 94 e9 a3 41 a9 08 01 09 cb
0x00000001736dca48:   08 fd 44 d3 a9 99 99 52 89 99 b9 72 08 7d 09 1b
0x00000001736dca58:   88 77 0f b9 9f 7b 0f b9 a8 92 40 91 08 21 27 91
0x00000001736dca68:   1f 41 00 f8 1f 19 00 79 75 0f 00 f9 e3 c3 00 91
0x00000001736dca78:   e4 63 00 91 e0 03 15 aa 21 00 80 52 e2 03 13 aa
0x00000001736dca88:   0a fd ff 97 60 05 00 34 e0 c3 00 91 01 00 80 52
0x00000001736dca98:   78 55 01 94 99 e2 00 91 e8 2b 40 f9 68 05 00 b4 


Register to memory mapping:

 x0=0x000000012006fe50 is an unknown value
 x1=0x0000000000000000 is an unknown value
 x2=0x0000000000000000 is an unknown value
 x3=0x0000000000000000 is an unknown value
 x4=0x0000000000000000 is an unknown value
 x5=0x0000000000000000 is an unknown value
 x6=0x000000000000000c is an unknown value
 x7=0x0000000000000000 is an unknown value
 x8=0x0000000000000000 is an unknown value
 x9=0x0000000000000000 is an unknown value
x10=0x0000600001e3a520 is an unknown value
x11=0x0000600001e3a9a0 is an unknown value
x12=0x000000016d4dda68 is pointing into the stack for thread: 0x000000011f009800
x13=0x00000000ffffffbb is an unknown value
x14=0x00000000000007fb is an unknown value
x15=0x00000000b0a227fb is an unknown value
x16=0x0000000186336a90: _platform_strcmp+0 in /usr/lib/system/libsystem_platform.dylib at 0x0000000186336000
x17=0x00000001e5c63cf0 is an unknown value
x18=0x0000000029ac2bd5 is an unknown value
x19=0x000000011016b102 is an unknown value
x20=0x00006000033023f0 is an unknown value
x21=0x0000000110148000 is an unknown value
x22=0x0000000000000000 is an unknown value
x23=0x0000000000000000 is an unknown value
x24=0x0000000000000000 is an unknown value
x25=0x000000016d4ddd70 is pointing into the stack for thread: 0x000000011f009800
x26=0x0000000110169b7c is an unknown value
x27=0x000000011016ca00 is an unknown value
x28=0x000000011016ba64 is an unknown value


Stack: [0x000000016d2dc000,0x000000016d4df000],  sp=0x000000016d4dd990,  free space=2054k
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
C  [libtesseract.5.dylib+0x289a8]  tesseract::Tesseract::recog_all_words(tesseract::PAGE_RES*, tesseract::ETEXT_DESC*, tesseract::TBOX const*, char const*, int)+0xd0
C  [libtesseract.5.dylib+0x5168]  tesseract::TessBaseAPI::Recognize(tesseract::ETEXT_DESC*)+0x300
C  [libtesseract.5.dylib+0x3f9c]  tesseract::TessBaseAPI::GetUTF8Text()+0x3c
C  [jna8644067996022288175.tmp+0x1004c]  ffi_prep_closure_loc+0x15ac
C  [jna8644067996022288175.tmp+0xea10]  ffi_call+0x520
C  [jna8644067996022288175.tmp+0x681c]  Java_com_sun_jna_Native_invokePointer+0x984
C  [jna8644067996022288175.tmp+0x5ec4]  Java_com_sun_jna_Native_invokePointer+0x2c
j  com.sun.jna.Native.invokePointer(Lcom/sun/jna/Function;JI[Ljava/lang/Object;)J+0
j  com.sun.jna.Function.invokePointer(I[Ljava/lang/Object;)Lcom/sun/jna/Pointer;+7
j  com.sun.jna.Function.invoke([Ljava/lang/Object;Ljava/lang/Class;ZI)Ljava/lang/Object;+385
j  com.sun.jna.Function.invoke(Ljava/lang/reflect/Method;[Ljava/lang/Class;Ljava/lang/Class;[Ljava/lang/Object;Ljava/util/Map;)Ljava/lang/Object;+271
j  com.sun.jna.Library$Handler.invoke(Ljava/lang/Object;Ljava/lang/reflect/Method;[Ljava/lang/Object;)Ljava/lang/Object;+390
j  com.sun.proxy.$Proxy0.TessBaseAPIGetUTF8Text(Lnet/sourceforge/tess4j/ITessAPI$TessBaseAPI;)Lcom/sun/jna/Pointer;+16
j  net.sourceforge.tess4j.Tesseract.getOCRText(Ljava/lang/String;I)Ljava/lang/String;+269
j  net.sourceforge.tess4j.Tesseract.doOCR(Ljava/util/List;Ljava/lang/String;Ljava/awt/Rectangle;)Ljava/lang/String;+69
j  net.sourceforge.tess4j.Tesseract.doOCR(Ljava/util/List;Ljava/awt/Rectangle;)Ljava/lang/String;+4
j  net.sourceforge.tess4j.Tesseract.doOCR(Ljava/awt/image/BufferedImage;Ljava/awt/Rectangle;)Ljava/lang/String;+6
j  net.sourceforge.tess4j.Tesseract.doOCR(Ljava/awt/image/BufferedImage;)Ljava/lang/String;+3
j  net.qiyuesuo.pdfbox.PdfToTextWithOCR.convertPdfToTextWithOCR(Ljava/lang/String;Ljava/lang/String;)V+140
j  net.qiyuesuo.pdfbox.PdfToTextWithOCR.main([Ljava/lang/String;)V+8
v  ~StubRoutines::call_stub
V  [libjvm.dylib+0x28cf3c]  JavaCalls::call_helper(JavaValue*, methodHandle*, JavaCallArguments*, Thread*)+0x764
V  [libjvm.dylib+0x2c2540]  jni_invoke_static(JNIEnv_*, JavaValue*, _jobject*, JNICallType, _jmethodID*, JNI_ArgumentPusher*, Thread*)+0x284
V  [libjvm.dylib+0x2c6190]  jni_CallStaticVoidMethod+0x1b0
C  [java+0x6430]  JavaMain+0xa48
C  [libsystem_pthread.dylib+0x7034]  _pthread_start+0x88
C  [libsystem_pthread.dylib+0x1e3c]  thread_start+0x8

Java frames: (J=compiled Java code, j=interpreted, Vv=VM code)
j  com.sun.jna.Native.invokePointer(Lcom/sun/jna/Function;JI[Ljava/lang/Object;)J+0
j  com.sun.jna.Function.invokePointer(I[Ljava/lang/Object;)Lcom/sun/jna/Pointer;+7
j  com.sun.jna.Function.invoke([Ljava/lang/Object;Ljava/lang/Class;ZI)Ljava/lang/Object;+385
j  com.sun.jna.Function.invoke(Ljava/lang/reflect/Method;[Ljava/lang/Class;Ljava/lang/Class;[Ljava/lang/Object;Ljava/util/Map;)Ljava/lang/Object;+271
j  com.sun.jna.Library$Handler.invoke(Ljava/lang/Object;Ljava/lang/reflect/Method;[Ljava/lang/Object;)Ljava/lang/Object;+390
j  com.sun.proxy.$Proxy0.TessBaseAPIGetUTF8Text(Lnet/sourceforge/tess4j/ITessAPI$TessBaseAPI;)Lcom/sun/jna/Pointer;+16
j  net.sourceforge.tess4j.Tesseract.getOCRText(Ljava/lang/String;I)Ljava/lang/String;+269
j  net.sourceforge.tess4j.Tesseract.doOCR(Ljava/util/List;Ljava/lang/String;Ljava/awt/Rectangle;)Ljava/lang/String;+69
j  net.sourceforge.tess4j.Tesseract.doOCR(Ljava/util/List;Ljava/awt/Rectangle;)Ljava/lang/String;+4
j  net.sourceforge.tess4j.Tesseract.doOCR(Ljava/awt/image/BufferedImage;Ljava/awt/Rectangle;)Ljava/lang/String;+6
j  net.sourceforge.tess4j.Tesseract.doOCR(Ljava/awt/image/BufferedImage;)Ljava/lang/String;+3
j  net.qiyuesuo.pdfbox.PdfToTextWithOCR.convertPdfToTextWithOCR(Ljava/lang/String;Ljava/lang/String;)V+140
j  net.qiyuesuo.pdfbox.PdfToTextWithOCR.main([Ljava/lang/String;)V+8
v  ~StubRoutines::call_stub

---------------  P R O C E S S  ---------------

Java Threads: ( => current thread )
  0x0000000121340000 JavaThread "JNA Cleaner" daemon [_thread_blocked, id=68883, stack(0x0000000173b84000,0x0000000173d87000)]
  0x0000000120e0b000 JavaThread "Java2D Queue Flusher" daemon [_thread_blocked, id=58895, stack(0x0000000173004000,0x0000000173207000)]
  0x0000000120dda800 JavaThread "AWT-Shutdown" [_thread_blocked, id=35343, stack(0x0000000170308000,0x000000017050b000)]
  0x0000000118953000 JavaThread "AppKit Thread" daemon [_thread_in_native, id=259, stack(0x000000016ca50000,0x000000016d24c000)]
  0x00000001211b6000 JavaThread "Java2D Disposer" daemon [_thread_blocked, id=23047, stack(0x00000001700fc000,0x00000001702ff000)]
  0x0000000120be4000 JavaThread "Service Thread" daemon [_thread_blocked, id=22019, stack(0x000000016fce4000,0x000000016fee7000)]
  0x000000011f199800 JavaThread "C1 CompilerThread3" daemon [_thread_blocked, id=32515, stack(0x000000016fad8000,0x000000016fcdb000)]
  0x000000011f199000 JavaThread "C2 CompilerThread2" daemon [_thread_blocked, id=18691, stack(0x000000016f8cc000,0x000000016facf000)]
  0x000000011f198000 JavaThread "C2 CompilerThread1" daemon [_thread_blocked, id=18947, stack(0x000000016f6c0000,0x000000016f8c3000)]
  0x000000011f197000 JavaThread "C2 CompilerThread0" daemon [_thread_blocked, id=18179, stack(0x000000016f4b4000,0x000000016f6b7000)]
  0x000000011f18c800 JavaThread "Monitor Ctrl-Break" daemon [_thread_in_native, id=17923, stack(0x000000016f2a8000,0x000000016f4ab000)]
  0x0000000120824800 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=19971, stack(0x000000016f09c000,0x000000016f29f000)]
  0x000000011880a800 JavaThread "Finalizer" daemon [_thread_blocked, id=13827, stack(0x000000016ed78000,0x000000016ef7b000)]
  0x0000000121020800 JavaThread "Reference Handler" daemon [_thread_blocked, id=13315, stack(0x000000016eb6c000,0x000000016ed6f000)]
=>0x000000011f009800 JavaThread "main" [_thread_in_native, id=4867, stack(0x000000016d2dc000,0x000000016d4df000)]

Other Threads:
  0x000000010f830800 VMThread [stack: 0x000000016e960000,0x000000016eb63000] [id=13059]
  0x0000000120beb000 WatcherThread [stack: 0x000000016fef0000,0x00000001700f3000] [id=22531]

VM state:not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

heap address: 0x00000005c0000000, size: 8192 MB, Compressed Oops mode: Zero based, Oop shift amount: 3
Narrow klass base: 0x0000000800000000, Narrow klass shift: 0
Compressed class space size: 1073741824 Address: 0x0000000800000000

Heap:
 PSYoungGen      total 153088K, used 110118K [0x0000000715580000, 0x0000000720000000, 0x00000007c0000000)
  eden space 131584K, 83% used [0x0000000715580000,0x000000071c109af8,0x000000071d600000)
  from space 21504K, 0% used [0x000000071eb00000,0x000000071eb00000,0x0000000720000000)
  to   space 21504K, 0% used [0x000000071d600000,0x000000071d600000,0x000000071eb00000)
 ParOldGen       total 349696K, used 0K [0x00000005c0000000, 0x00000005d5580000, 0x0000000715580000)
  object space 349696K, 0% used [0x00000005c0000000,0x00000005c0000000,0x00000005d5580000)
 Metaspace       used 13040K, capacity 13390K, committed 13696K, reserved 1060864K
  class space    used 1433K, capacity 1537K, committed 1664K, reserved 1048576K

Card table byte_map: [0x000000010cab4000,0x000000010dab8000] byte_map_base: 0x0000000109cb4000

Marking Bits: (ParMarkBitMap*) 0x0000000104453d30
 Begin Bits: [0x0000000121800000, 0x0000000129800000)
 End Bits:   [0x0000000129800000, 0x0000000131800000)

Polling page: 0x0000000102bdc000

CodeCache: size=131072Kb used=5821Kb max_used=5833Kb free=125250Kb
 bounds [0x0000000104ab4000, 0x0000000105074000, 0x000000010cab4000]
 total_blobs=2002 nmethods=1516 adapters=401
 compilation: enabled

Compilation events (10 events):
Event: 0.720 Thread 0x000000011f199800 1512  s    3       java.io.ExpiringCache::put (70 bytes)
Event: 0.720 Thread 0x000000011f199800 nmethod 1512 0x0000000105066a10 code [0x0000000105066c00, 0x00000001050673a0]
Event: 0.720 Thread 0x000000011f199800 1513       3       java.io.ExpiringCache$Entry::<init> (15 bytes)
Event: 0.721 Thread 0x000000011f199800 nmethod 1513 0x0000000105060290 code [0x0000000105060400, 0x0000000105060590]
Event: 0.721 Thread 0x000000011f199800 1514       1       java.io.ExpiringCache::access$000 (5 bytes)
Event: 0.721 Thread 0x000000011f199800 nmethod 1514 0x0000000105060050 code [0x0000000105060180, 0x0000000105060210]
Event: 0.721 Thread 0x000000011f199800 1515       1       sun.java2d.loops.SurfaceType::getSuperType (5 bytes)
Event: 0.721 Thread 0x000000011f199800 nmethod 1515 0x000000010505fe10 code [0x000000010505ff40, 0x000000010505ffd0]
Event: 0.721 Thread 0x000000011f198000 1516       4       sun.java2d.loops.GraphicsPrimitiveMgr$2::compare (39 bytes)
Event: 0.721 Thread 0x000000011f198000 nmethod 1516 0x000000010506a4d0 code [0x000000010506a640, 0x000000010506a750]

GC Heap History (0 events):
No events

Deoptimization events (10 events):
Event: 0.680 Thread 0x000000011f009800 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000000104db4034 method=java.lang.AbstractStringBuilder.append(Ljava/lang/String;)Ljava/lang/AbstractStringBuilder; @ 1
Event: 0.699 Thread 0x000000011f009800 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000000104ee7cf0 method=java.util.HashMap.getNode(ILjava/lang/Object;)Ljava/util/HashMap$Node; @ 62
Event: 0.700 Thread 0x000000011f009800 Uncommon trap: reason=class_check action=maybe_recompile pc=0x0000000104e4ff04 method=java.util.HashMap.putVal(ILjava/lang/Object;Ljava/lang/Object;ZZ)Ljava/lang/Object; @ 203
Event: 0.701 Thread 0x000000011f009800 Uncommon trap: reason=class_check action=maybe_recompile pc=0x0000000104e4ff04 method=java.util.HashMap.putVal(ILjava/lang/Object;Ljava/lang/Object;ZZ)Ljava/lang/Object; @ 203
Event: 0.702 Thread 0x000000011f009800 Uncommon trap: reason=class_check action=maybe_recompile pc=0x0000000104e4ff04 method=java.util.HashMap.putVal(ILjava/lang/Object;Ljava/lang/Object;ZZ)Ljava/lang/Object; @ 203
Event: 0.702 Thread 0x000000011f009800 Uncommon trap: reason=class_check action=maybe_recompile pc=0x0000000104e4e2c0 method=java.util.HashMap.putVal(ILjava/lang/Object;Ljava/lang/Object;ZZ)Ljava/lang/Object; @ 203
Event: 0.703 Thread 0x000000011f009800 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000000010504b328 method=java.io.DataOutputStream.writeByte(I)V @ 5
Event: 0.703 Thread 0x000000011f009800 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000000010504b328 method=java.io.DataOutputStream.writeByte(I)V @ 5
Event: 0.703 Thread 0x000000011f009800 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000000010504b328 method=java.io.DataOutputStream.writeByte(I)V @ 5
Event: 0.703 Thread 0x000000011f009800 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000000010504b328 method=java.io.DataOutputStream.writeByte(I)V @ 5

Classes redefined (0 events):
No events

Internal exceptions (10 events):
Event: 0.068 Thread 0x000000011f009800 Exception <a 'java/security/PrivilegedActionException'> (0x000000071598fb20) thrown at [/System/Volumes/Data/jenkins/workspace/8-2-build-macosx-aarch64-Xcode12.4-sans-NAS/jdk8u401/683/hotspot/src/share/vm/prims/jvm.cpp, line 1513]
Event: 0.167 Thread 0x000000011f009800 Exception <a 'java/security/PrivilegedActionException'> (0x0000000716dc8a30) thrown at [/System/Volumes/Data/jenkins/workspace/8-2-build-macosx-aarch64-Xcode12.4-sans-NAS/jdk8u401/683/hotspot/src/share/vm/prims/jvm.cpp, line 1513]
Event: 0.167 Thread 0x000000011f009800 Exception <a 'java/security/PrivilegedActionException'> (0x0000000716dc8c40) thrown at [/System/Volumes/Data/jenkins/workspace/8-2-build-macosx-aarch64-Xcode12.4-sans-NAS/jdk8u401/683/hotspot/src/share/vm/prims/jvm.cpp, line 1513]
Event: 0.184 Thread 0x000000011f009800 Exception <a 'java/io/FileNotFoundException'> (0x00000007175700e0) thrown at [/System/Volumes/Data/jenkins/workspace/8-2-build-macosx-aarch64-Xcode12.4-sans-NAS/jdk8u401/683/hotspot/src/share/vm/prims/jni.cpp, line 710]
Event: 0.219 Thread 0x000000011f009800 Exception <a 'java/lang/NoSuchFieldError': method resolution failed> (0x0000000717a98318) thrown at [/System/Volumes/Data/jenkins/workspace/8-2-build-macosx-aarch64-Xcode12.4-sans-NAS/jdk8u401/683/hotspot/src/share/vm/prims/methodHandles.cpp, line 1188]
Event: 0.220 Thread 0x000000011f009800 Exception <a 'java/lang/NoSuchFieldError': method resolution failed> (0x0000000717aa6378) thrown at [/System/Volumes/Data/jenkins/workspace/8-2-build-macosx-aarch64-Xcode12.4-sans-NAS/jdk8u401/683/hotspot/src/share/vm/prims/methodHandles.cpp, line 1188]
Event: 0.228 Thread 0x000000011f009800 Exception <a 'java/lang/NoSuchMethodError': sun.misc.Unsafe.invokeCleaner(Ljava/nio/ByteBuffer;)V> (0x0000000717b47670) thrown at [/System/Volumes/Data/jenkins/workspace/8-2-build-macosx-aarch64-Xcode12.4-sans-NAS/jdk8u401/683/hotspot/src/share/vm/interpr
Event: 0.270 Thread 0x000000011f009800 Exception <a 'java/io/FileNotFoundException'> (0x0000000717fde558) thrown at [/System/Volumes/Data/jenkins/workspace/8-2-build-macosx-aarch64-Xcode12.4-sans-NAS/jdk8u401/683/hotspot/src/share/vm/prims/jni.cpp, line 710]
Event: 0.270 Thread 0x000000011f009800 Exception <a 'java/io/FileNotFoundException'> (0x0000000717fdf048) thrown at [/System/Volumes/Data/jenkins/workspace/8-2-build-macosx-aarch64-Xcode12.4-sans-NAS/jdk8u401/683/hotspot/src/share/vm/prims/jni.cpp, line 710]
Event: 0.680 Thread 0x000000011f009800 Implicit null exception at 0x0000000104db3bb0 to 0x0000000104db4024

Events (10 events):
Event: 0.720 loading class sun/awt/image/ByteComponentRaster done
Event: 0.720 loading class sun/awt/image/ByteInterleavedRaster done
Event: 0.721 loading class java/awt/image/VolatileImage
Event: 0.721 loading class java/awt/image/VolatileImage done
Event: 0.721 loading class sun/awt/image/MultiResolutionImage
Event: 0.721 loading class sun/awt/image/MultiResolutionImage done
Event: 0.721 loading class sun/awt/image/ToolkitImage
Event: 0.721 loading class sun/awt/image/ToolkitImage done
Event: 0.722 loading class com/sun/jna/Native$Buffers
Event: 0.722 loading class com/sun/jna/Native$Buffers done


Dynamic libraries:
0x00000001a0036000 	/System/Library/Frameworks/Cocoa.framework/Versions/A/Cocoa
0x0000000189b8b000 	/System/Library/Frameworks/AppKit.framework/Versions/C/AppKit
0x000000018cbd3000 	/System/Library/Frameworks/CoreData.framework/Versions/A/CoreData
0x0000000187497000 	/System/Library/Frameworks/Foundation.framework/Versions/C/Foundation
0x0000000192bb6000 	/usr/lib/libSystem.B.dylib
0x000000018aeb7000 	/System/Library/PrivateFrameworks/UIFoundation.framework/Versions/A/UIFoundation
0x00000001fceb6000 	/System/Library/PrivateFrameworks/CollectionViewCore.framework/Versions/A/CollectionViewCore
0x0000000199d63000 	/System/Library/PrivateFrameworks/RemoteViewServices.framework/Versions/A/RemoteViewServices
0x0000000190c56000 	/System/Library/PrivateFrameworks/XCTTargetBootstrap.framework/Versions/A/XCTTargetBootstrap
0x00000001951a1000 	/System/Library/PrivateFrameworks/InternationalSupport.framework/Versions/A/InternationalSupport
0x0000000195229000 	/System/Library/PrivateFrameworks/UserActivity.framework/Versions/A/UserActivity
0x000000021f21f000 	/System/Library/PrivateFrameworks/WindowManagement.framework/Versions/A/WindowManagement
0x0000000187120000 	/System/Library/Frameworks/SystemConfiguration.framework/Versions/A/SystemConfiguration
0x0000000194642000 	/usr/lib/libspindump.dylib
0x000000018b06c000 	/System/Library/Frameworks/UniformTypeIdentifiers.framework/Versions/A/UniformTypeIdentifiers
0x000000018eba9000 	/usr/lib/libapp_launch_measurement.dylib
0x000000018dfbb000 	/System/Library/PrivateFrameworks/CoreAnalytics.framework/Versions/A/CoreAnalytics
0x000000018ebb0000 	/System/Library/PrivateFrameworks/CoreAutoLayout.framework/Versions/A/CoreAutoLayout
0x0000000190480000 	/System/Library/Frameworks/Metal.framework/Versions/A/Metal
0x00000001913c6000 	/usr/lib/liblangid.dylib
0x0000000190c5c000 	/System/Library/PrivateFrameworks/CoreSVG.framework/Versions/A/CoreSVG
0x000000018ba26000 	/System/Library/PrivateFrameworks/SkyLight.framework/Versions/A/SkyLight
0x000000018bebe000 	/System/Library/Frameworks/CoreGraphics.framework/Versions/A/CoreGraphics
0x000000019a4ab000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Accelerate
0x00000001944a0000 	/System/Library/PrivateFrameworks/IconServices.framework/Versions/A/IconServices
0x000000019045e000 	/System/Library/Frameworks/IOSurface.framework/Versions/A/IOSurface
0x000000018dfea000 	/usr/lib/libDiagnosticMessagesClient.dylib
0x0000000192af9000 	/usr/lib/libz.1.dylib
0x000000019dba3000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/ApplicationServices
0x0000000190c3e000 	/System/Library/PrivateFrameworks/DFRFoundation.framework/Versions/A/DFRFoundation
0x00000001893f7000 	/usr/lib/libicucore.A.dylib
0x0000000196207000 	/System/Library/Frameworks/AudioToolbox.framework/Versions/A/AudioToolbox
0x00000001951ae000 	/System/Library/PrivateFrameworks/DataDetectorsCore.framework/Versions/A/DataDetectorsCore
0x00000001ae4a0000 	/System/Library/PrivateFrameworks/TextInput.framework/Versions/A/TextInput
0x000000018b979000 	/usr/lib/libMobileGestalt.dylib
0x000000019097e000 	/System/Library/Frameworks/Carbon.framework/Versions/A/Frameworks/HIToolbox.framework/Versions/A/HIToolbox
0x000000018e4bc000 	/System/Library/Frameworks/QuartzCore.framework/Versions/A/QuartzCore
0x0000000189035000 	/System/Library/Frameworks/Security.framework/Versions/A/Security
0x0000000199da3000 	/System/Library/Frameworks/Carbon.framework/Versions/A/Frameworks/SpeechRecognition.framework/Versions/A/SpeechRecognition
0x000000018e8d7000 	/System/Library/PrivateFrameworks/CoreUI.framework/Versions/A/CoreUI
0x0000000188941000 	/System/Library/Frameworks/CoreAudio.framework/Versions/A/CoreAudio
0x000000018e0ce000 	/System/Library/Frameworks/DiskArbitration.framework/Versions/A/DiskArbitration
0x0000000194a99000 	/System/Library/PrivateFrameworks/MultitouchSupport.framework/Versions/A/MultitouchSupport
0x000000018b977000 	/usr/lib/libenergytrace.dylib
0x00000001a400b000 	/System/Library/PrivateFrameworks/RenderBox.framework/Versions/A/RenderBox
0x0000000189a41000 	/System/Library/Frameworks/IOKit.framework/Versions/A/IOKit
0x000000019a1a7000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/CoreServices
0x000000018eb35000 	/System/Library/PrivateFrameworks/PerformanceAnalysis.framework/Versions/A/PerformanceAnalysis
0x00000001eeb0b000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/OpenGL
0x000000018ebfa000 	/usr/lib/libxml2.2.dylib
0x0000000191ff3000 	/System/Library/PrivateFrameworks/MobileKeyBag.framework/Versions/A/MobileKeyBag
0x0000000185f34000 	/usr/lib/libobjc.A.dylib
0x000000018621e000 	/usr/lib/libc++.1.dylib
0x000000019a124000 	/System/Library/Frameworks/Accessibility.framework/Versions/A/Accessibility
0x000000018c59c000 	/System/Library/Frameworks/ColorSync.framework/Versions/A/ColorSync
0x000000018636a000 	/System/Library/Frameworks/CoreFoundation.framework/Versions/A/CoreFoundation
0x0000000190fb2000 	/System/Library/Frameworks/CoreImage.framework/Versions/A/CoreImage
0x000000018874d000 	/System/Library/Frameworks/CoreText.framework/Versions/A/CoreText
0x00000001f330d000 	/System/Library/Frameworks/CoreTransferable.framework/Versions/A/CoreTransferable
0x00000001f37ef000 	/System/Library/Frameworks/DeveloperToolsSupport.framework/Versions/A/DeveloperToolsSupport
0x0000000190c97000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/ImageIO
0x00000001f773d000 	/System/Library/Frameworks/Symbols.framework/Versions/A/Symbols
0x0000000192bbc000 	/System/Library/PrivateFrameworks/SoftLinking.framework/Versions/A/SoftLinking
0x0000000195bd9000 	/usr/lib/swift/libswiftCore.dylib
0x00000001ab1d4000 	/usr/lib/swift/libswiftCoreFoundation.dylib
0x00000001a8e65000 	/usr/lib/swift/libswiftCoreGraphics.dylib
0x00000001ab220000 	/usr/lib/swift/libswiftCoreImage.dylib
0x00000001a8e6c000 	/usr/lib/swift/libswiftDarwin.dylib
0x000000019b81b000 	/usr/lib/swift/libswiftDispatch.dylib
0x00000001ab221000 	/usr/lib/swift/libswiftIOKit.dylib
0x00000001b6be0000 	/usr/lib/swift/libswiftMetal.dylib
0x00000001c358a000 	/usr/lib/swift/libswiftOSLog.dylib
0x000000019e00f000 	/usr/lib/swift/libswiftObjectiveC.dylib
0x00000001ba80b000 	/usr/lib/swift/libswiftQuartzCore.dylib
0x00000001be862000 	/usr/lib/swift/libswiftUniformTypeIdentifiers.dylib
0x00000001ab1ea000 	/usr/lib/swift/libswiftXPC.dylib
0x000000022752c000 	/usr/lib/swift/libswift_Concurrency.dylib
0x000000019e013000 	/usr/lib/swift/libswiftos.dylib
0x00000001ae404000 	/usr/lib/swift/libswiftsimd.dylib
0x0000000192d6a000 	/usr/lib/libcompression.dylib
0x00000001950fb000 	/System/Library/PrivateFrameworks/TextureIO.framework/Versions/A/TextureIO
0x0000000194153000 	/usr/lib/libate.dylib
0x0000000192bb0000 	/usr/lib/system/libcache.dylib
0x0000000192b6b000 	/usr/lib/system/libcommonCrypto.dylib
0x0000000192b97000 	/usr/lib/system/libcompiler_rt.dylib
0x0000000192b8d000 	/usr/lib/system/libcopyfile.dylib
0x000000018607f000 	/usr/lib/system/libcorecrypto.dylib
0x0000000186155000 	/usr/lib/system/libdispatch.dylib
0x0000000186310000 	/usr/lib/system/libdyld.dylib
0x0000000192ba6000 	/usr/lib/system/libkeymgr.dylib
0x0000000192b43000 	/usr/lib/system/libmacho.dylib
0x00000001920d9000 	/usr/lib/system/libquarantine.dylib
0x0000000192ba3000 	/usr/lib/system/libremovefile.dylib
0x000000018b9ed000 	/usr/lib/system/libsystem_asl.dylib
0x0000000186018000 	/usr/lib/system/libsystem_blocks.dylib
0x000000018619f000 	/usr/lib/system/libsystem_c.dylib
0x0000000192b9b000 	/usr/lib/system/libsystem_collections.dylib
0x00000001913b5000 	/usr/lib/system/libsystem_configuration.dylib
0x0000000190434000 	/usr/lib/system/libsystem_containermanager.dylib
0x00000001927df000 	/usr/lib/system/libsystem_coreservices.dylib
0x00000001896b0000 	/usr/lib/system/libsystem_darwin.dylib
0x0000000227859000 	/usr/lib/system/libsystem_darwindirectory.dylib
0x0000000192ba7000 	/usr/lib/system/libsystem_dnssd.dylib
0x000000018619c000 	/usr/lib/system/libsystem_featureflags.dylib
0x000000018633d000 	/usr/lib/system/libsystem_info.dylib
0x0000000192b08000 	/usr/lib/system/libsystem_m.dylib
0x000000018611e000 	/usr/lib/system/libsystem_malloc.dylib
0x000000018b95d000 	/usr/lib/system/libsystem_networkextension.dylib
0x0000000189b23000 	/usr/lib/system/libsystem_notify.dylib
0x00000001913ba000 	/usr/lib/system/libsystem_sandbox.dylib
0x0000000192ba0000 	/usr/lib/system/libsystem_secinit.dylib
0x00000001862c8000 	/usr/lib/system/libsystem_kernel.dylib
0x0000000186336000 	/usr/lib/system/libsystem_platform.dylib
0x0000000186303000 	/usr/lib/system/libsystem_pthread.dylib
0x000000018d3c5000 	/usr/lib/system/libsystem_symptoms.dylib
0x0000000186064000 	/usr/lib/system/libsystem_trace.dylib
0x0000000192b79000 	/usr/lib/system/libunwind.dylib
0x000000018601d000 	/usr/lib/system/libxpc.dylib
0x00000001862ac000 	/usr/lib/libc++abi.dylib
0x0000000192b85000 	/usr/lib/liboah.dylib
0x0000000194006000 	/usr/lib/liblzma.5.dylib
0x0000000192bb8000 	/usr/lib/libfakelink.dylib
0x000000018b589000 	/System/Library/Frameworks/CFNetwork.framework/Versions/A/CFNetwork
0x0000000192c0c000 	/usr/lib/libarchive.2.dylib
0x000000019808e000 	/System/Library/Frameworks/Combine.framework/Versions/A/Combine
0x00000001fceca000 	/System/Library/PrivateFrameworks/CollectionsInternal.framework/Versions/A/CollectionsInternal
0x0000000212699000 	/System/Library/PrivateFrameworks/ReflectionInternal.framework/Versions/A/ReflectionInternal
0x0000000212c46000 	/System/Library/PrivateFrameworks/RuntimeInternal.framework/Versions/A/RuntimeInternal
0x0000000227675000 	/usr/lib/swift/libswift_StringProcessing.dylib
0x00000001899c0000 	/System/Library/PrivateFrameworks/CoreServicesInternal.framework/Versions/A/CoreServicesInternal
0x0000000192100000 	/usr/lib/libbsm.0.dylib
0x0000000192b4b000 	/usr/lib/system/libkxld.dylib
0x000000018eb71000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/FSEvents.framework/Versions/A/FSEvents
0x00000001896bb000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/CarbonCore.framework/Versions/A/CarbonCore
0x000000018e033000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/Metadata.framework/Versions/A/Metadata
0x00000001927e5000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/OSServices.framework/Versions/A/OSServices
0x0000000192c95000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/SearchKit.framework/Versions/A/SearchKit
0x000000018d347000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/AE.framework/Versions/A/AE
0x0000000186842000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/LaunchServices.framework/Versions/A/LaunchServices
0x0000000193faf000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/DictionaryServices.framework/Versions/A/DictionaryServices
0x000000018eb7e000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/SharedFileList.framework/Versions/A/SharedFileList
0x0000000192d34000 	/usr/lib/libapple_nghttp2.dylib
0x000000018cfc9000 	/usr/lib/libsqlite3.dylib
0x000000018d3ce000 	/System/Library/Frameworks/Network.framework/Versions/A/Network
0x0000000225fc2000 	/usr/lib/libCoreEntitlements.dylib
0x000000020d9e4000 	/System/Library/PrivateFrameworks/MessageSecurity.framework/Versions/A/MessageSecurity
0x000000018cfaf000 	/System/Library/PrivateFrameworks/ProtocolBuffer.framework/Versions/A/ProtocolBuffer
0x000000019270e000 	/System/Library/PrivateFrameworks/AppleFSCompression.framework/Versions/A/AppleFSCompression
0x00000001920e8000 	/usr/lib/libcoretls.dylib
0x000000019401f000 	/usr/lib/libcoretls_cfhelpers.dylib
0x0000000192d64000 	/usr/lib/libpam.2.dylib
0x0000000194090000 	/usr/lib/libxar.1.dylib
0x0000000194477000 	/usr/lib/libheimdal-asn1.dylib
0x000000018b588000 	/usr/lib/libnetwork.dylib
0x0000000192bbd000 	/usr/lib/libpcap.A.dylib
0x000000018d3bb000 	/usr/lib/libdns_services.dylib
0x00000001913c2000 	/System/Library/PrivateFrameworks/AppleSystemInfo.framework/Versions/A/AppleSystemInfo
0x0000000191de9000 	/System/Library/PrivateFrameworks/IOMobileFramebuffer.framework/Versions/A/IOMobileFramebuffer
0x00000002275c9000 	/usr/lib/swift/libswift_RegexParser.dylib
0x00000001927d2000 	/usr/lib/libbz2.1.0.dylib
0x00000001920dc000 	/usr/lib/libCheckFix.dylib
0x000000018ba05000 	/System/Library/PrivateFrameworks/TCC.framework/Versions/A/TCC
0x00000001913c8000 	/System/Library/PrivateFrameworks/CoreNLP.framework/Versions/A/CoreNLP
0x000000018dfec000 	/System/Library/PrivateFrameworks/MetadataUtilities.framework/Versions/A/MetadataUtilities
0x0000000192112000 	/usr/lib/libmecab.dylib
0x00000001871b3000 	/usr/lib/libCRFSuite.dylib
0x000000019216e000 	/usr/lib/libgermantok.dylib
0x0000000192d0c000 	/usr/lib/libThaiTokenizer.dylib
0x000000018e0d7000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vImage.framework/Versions/A/vImage
0x000000019a17e000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/vecLib
0x00000001940d7000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libvMisc.dylib
0x0000000191cc5000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libvDSP.dylib
0x0000000186c42000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libBLAS.dylib
0x0000000192e3d000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libLAPACK.dylib
0x0000000192171000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libLinearAlgebra.dylib
0x0000000192d4f000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libSparseBLAS.dylib
0x0000000192e38000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libQuadrature.dylib
0x00000001914ea000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libBNNS.dylib
0x00000001870b9000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libSparse.dylib
0x000000020c25a000 	/System/Library/PrivateFrameworks/MIL.framework/Versions/A/MIL
0x0000000192bf3000 	/usr/lib/libiconv.2.dylib
0x0000000192b3f000 	/usr/lib/libcharset.1.dylib
0x000000018eb51000 	/System/Library/Frameworks/OpenDirectory.framework/Versions/A/Frameworks/CFOpenDirectory.framework/Versions/A/CFOpenDirectory
0x000000018eb41000 	/System/Library/Frameworks/OpenDirectory.framework/Versions/A/OpenDirectory
0x0000000194021000 	/System/Library/PrivateFrameworks/APFS.framework/Versions/A/APFS
0x0000000192019000 	/System/Library/Frameworks/SecurityFoundation.framework/Versions/A/SecurityFoundation
0x000000019409f000 	/usr/lib/libutil.dylib
0x000000020a441000 	/System/Library/PrivateFrameworks/InstalledContentLibrary.framework/Versions/A/InstalledContentLibrary
0x0000000189a00000 	/System/Library/PrivateFrameworks/CoreServicesStore.framework/Versions/A/CoreServicesStore
0x00000001faa82000 	/System/Library/PrivateFrameworks/AppleMobileFileIntegrity.framework/Versions/A/AppleMobileFileIntegrity
0x00000001ab1b2000 	/usr/lib/libmis.dylib
0x00000001bacd2000 	/System/Library/PrivateFrameworks/MobileSystemServices.framework/Versions/A/MobileSystemServices
0x00000001ebf73000 	/System/Library/PrivateFrameworks/ConfigProfileHelper.framework/Versions/A/ConfigProfileHelper
0x0000000192d0e000 	/System/Library/PrivateFrameworks/AppleSauce.framework/Versions/A/AppleSauce
0x00000001880df000 	/System/Library/PrivateFrameworks/LanguageModeling.framework/Versions/A/LanguageModeling
0x00000001940a3000 	/usr/lib/libxslt.1.dylib
0x0000000192bfa000 	/usr/lib/libcmph.dylib
0x0000000191db6000 	/System/Library/PrivateFrameworks/CoreEmoji.framework/Versions/A/CoreEmoji
0x00000001914e4000 	/System/Library/PrivateFrameworks/LinguisticData.framework/Versions/A/LinguisticData
0x0000000186fcc000 	/System/Library/PrivateFrameworks/Lexicon.framework/Versions/A/Lexicon
0x00000001920a7000 	/System/Library/PrivateFrameworks/BackgroundTaskManagement.framework/Versions/A/BackgroundTaskManagement
0x00000002261ab000 	/usr/lib/libTLE.dylib
0x000000019495f000 	/System/Library/PrivateFrameworks/AppleJPEG.framework/Versions/A/AppleJPEG
0x000000019445c000 	/usr/lib/libexpat.1.dylib
0x0000000194f59000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libPng.dylib
0x0000000194f84000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libTIFF.dylib
0x000000019506f000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libGIF.dylib
0x00000001949a5000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libJP2.dylib
0x0000000195014000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libJPEG.dylib
0x000000019500b000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libRadiance.dylib
0x0000000190819000 	/System/Library/PrivateFrameworks/FontServices.framework/libFontParser.dylib
0x000000018d2e5000 	/System/Library/PrivateFrameworks/RunningBoardServices.framework/Versions/A/RunningBoardServices
0x00000001a08cb000 	/System/Library/PrivateFrameworks/IOSurfaceAccelerator.framework/Versions/A/IOSurfaceAccelerator
0x0000000194a95000 	/System/Library/PrivateFrameworks/WatchdogClient.framework/Versions/A/WatchdogClient
0x00000001882c2000 	/System/Library/Frameworks/CoreDisplay.framework/Versions/A/CoreDisplay
0x00000001906d2000 	/System/Library/Frameworks/CoreMedia.framework/Versions/A/CoreMedia
0x0000000190476000 	/System/Library/PrivateFrameworks/IOAccelerator.framework/Versions/A/IOAccelerator
0x000000018ece4000 	/System/Library/Frameworks/CoreVideo.framework/Versions/A/CoreVideo
0x0000000192d62000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/MetalPerformanceShaders
0x0000000194ad8000 	/System/Library/Frameworks/VideoToolbox.framework/Versions/A/VideoToolbox
0x000000018ed7f000 	/System/Library/PrivateFrameworks/UserManagement.framework/Versions/A/UserManagement
0x000000018d214000 	/System/Library/PrivateFrameworks/BaseBoard.framework/Versions/A/BaseBoard
0x00000001913c0000 	/System/Library/PrivateFrameworks/AggregateDictionary.framework/Versions/A/AggregateDictionary
0x00000001faa02000 	/System/Library/PrivateFrameworks/AppleKeyStore.framework/Versions/A/AppleKeyStore
0x0000000195006000 	/System/Library/PrivateFrameworks/GPUWrangler.framework/Versions/A/GPUWrangler
0x0000000194fe6000 	/System/Library/PrivateFrameworks/IOPresentment.framework/Versions/A/IOPresentment
0x000000019500e000 	/System/Library/PrivateFrameworks/DSExternalDisplay.framework/Versions/A/DSExternalDisplay
0x00000002065af000 	/System/Library/PrivateFrameworks/GPUCompiler.framework/Versions/32023/Libraries/libllvm-flatbuffers.dylib
0x00000001eeafe000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libCoreFSCache.dylib
0x00000002037e7000 	/System/Library/PrivateFrameworks/GPUCompiler.framework/Versions/32023/Libraries/libGPUCompilerUtils.dylib
0x0000000195075000 	/System/Library/PrivateFrameworks/CMCaptureCore.framework/Versions/A/CMCaptureCore
0x00000001f38f4000 	/System/Library/Frameworks/ExtensionFoundation.framework/Versions/A/ExtensionFoundation
0x000000019c060000 	/System/Library/PrivateFrameworks/CoreTime.framework/Versions/A/CoreTime
0x000000019462d000 	/System/Library/PrivateFrameworks/AppServerSupport.framework/Versions/A/AppServerSupport
0x0000000196b87000 	/System/Library/PrivateFrameworks/perfdata.framework/Versions/A/perfdata
0x00000001883e6000 	/System/Library/PrivateFrameworks/AudioToolboxCore.framework/Versions/A/AudioToolboxCore
0x00000001906a8000 	/System/Library/PrivateFrameworks/caulk.framework/Versions/A/caulk
0x0000000196402000 	/usr/lib/libAudioStatistics.dylib
0x00000001aa50e000 	/System/Library/PrivateFrameworks/SystemPolicy.framework/Versions/A/SystemPolicy
0x00000001966da000 	/usr/lib/libSMC.dylib
0x000000019fec5000 	/System/Library/Frameworks/CoreMIDI.framework/Versions/A/CoreMIDI
0x0000000194f20000 	/usr/lib/libAudioToolboxUtility.dylib
0x00000001a5cb0000 	/System/Library/PrivateFrameworks/OSAServicesClient.framework/Versions/A/OSAServicesClient
0x0000000196b95000 	/usr/lib/libperfcheck.dylib
0x0000000194346000 	/System/Library/PrivateFrameworks/PlugInKit.framework/Versions/A/PlugInKit
0x000000019200b000 	/System/Library/PrivateFrameworks/AssertionServices.framework/Versions/A/AssertionServices
0x000000019efd9000 	/System/Library/PrivateFrameworks/ASEProcessing.framework/Versions/A/ASEProcessing
0x00000001c6943000 	/System/Library/PrivateFrameworks/Symbolication.framework/Versions/A/Symbolication
0x0000000211706000 	/System/Library/PrivateFrameworks/PhotosensitivityProcessing.framework/Versions/A/PhotosensitivityProcessing
0x00000001945df000 	/System/Library/PrivateFrameworks/GraphVisualizer.framework/Versions/A/GraphVisualizer
0x00000001eeb60000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGLU.dylib
0x00000001eeb1f000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGFXShared.dylib
0x00000001eecf8000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGL.dylib
0x00000001eeb28000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGLImage.dylib
0x00000001eeb1c000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libCVMSPluginSupport.dylib
0x0000000226164000 	/usr/lib/libRosetta.dylib
0x00000001eeb05000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libCoreVMClient.dylib
0x0000000201ba1000 	/System/Library/PrivateFrameworks/FontServices.framework/Versions/A/FontServices
0x00000001945eb000 	/System/Library/PrivateFrameworks/OTSVG.framework/Versions/A/OTSVG
0x000000018e885000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ATS.framework/Versions/A/Resources/libFontRegistry.dylib
0x0000000194637000 	/System/Library/PrivateFrameworks/FontServices.framework/libhvf.dylib
0x0000000201ba2000 	/System/Library/PrivateFrameworks/FontServices.framework/libXTFontStaticRegistryData.dylib
0x0000000191337000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSCore.framework/Versions/A/MPSCore
0x0000000192681000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSImage.framework/Versions/A/MPSImage
0x0000000192186000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSNeuralNetwork.framework/Versions/A/MPSNeuralNetwork
0x0000000192574000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSMatrix.framework/Versions/A/MPSMatrix
0x0000000192380000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSRayIntersector.framework/Versions/A/MPSRayIntersector
0x00000001925a3000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSNDArray.framework/Versions/A/MPSNDArray
0x00000001f4fcc000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSFunctions.framework/Versions/A/MPSFunctions
0x00000001f4fae000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSBenchmarkLoop.framework/Versions/A/MPSBenchmarkLoop
0x0000000186af7000 	/System/Library/PrivateFrameworks/MetalTools.framework/Versions/A/MetalTools
0x00000001af5c1000 	/System/Library/PrivateFrameworks/IOAccelMemoryInfo.framework/Versions/A/IOAccelMemoryInfo
0x00000001babdf000 	/System/Library/PrivateFrameworks/kperf.framework/Versions/A/kperf
0x00000001ab1c6000 	/System/Library/PrivateFrameworks/GPURawCounter.framework/Versions/A/GPURawCounter
0x000000019bf50000 	/System/Library/PrivateFrameworks/CoreSymbolication.framework/Versions/A/CoreSymbolication
0x00000001ab183000 	/System/Library/PrivateFrameworks/MallocStackLogging.framework/Versions/A/MallocStackLogging
0x00000001941ed000 	/System/Library/PrivateFrameworks/CrashReporterSupport.framework/Versions/A/CrashReporterSupport
0x000000019bf0b000 	/System/Library/PrivateFrameworks/DebugSymbols.framework/Versions/A/DebugSymbols
0x00000001b9f1d000 	/System/Library/PrivateFrameworks/OSAnalytics.framework/Versions/A/OSAnalytics
0x000000021d6d9000 	/System/Library/PrivateFrameworks/VideoToolboxParavirtualizationSupport.framework/Versions/A/VideoToolboxParavirtualizationSupport
0x0000000194410000 	/System/Library/PrivateFrameworks/AppleVA.framework/Versions/A/AppleVA
0x000000019644a000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ATS.framework/Versions/A/ATS
0x000000018c74b000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/HIServices.framework/Versions/A/HIServices
0x0000000195083000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/PrintCore.framework/Versions/A/PrintCore
0x0000000196833000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/QD.framework/Versions/A/QD
0x0000000196827000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ColorSyncLegacy.framework/Versions/A/ColorSyncLegacy
0x000000019641a000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/SpeechSynthesis.framework/Versions/A/SpeechSynthesis
0x000000019503f000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ATSUI.framework/Versions/A/ATSUI
0x00000001967ba000 	/usr/lib/libcups.2.dylib
0x0000000196ba3000 	/System/Library/Frameworks/Kerberos.framework/Versions/A/Kerberos
0x0000000196bb4000 	/System/Library/Frameworks/GSS.framework/Versions/A/GSS
0x00000001964c9000 	/usr/lib/libresolv.9.dylib
0x0000000194648000 	/System/Library/PrivateFrameworks/Heimdal.framework/Versions/A/Heimdal
0x000000019df7b000 	/System/Library/Frameworks/Kerberos.framework/Versions/A/Libraries/libHeimdalProxy.dylib
0x0000000196c0e000 	/System/Library/PrivateFrameworks/CommonAuth.framework/Versions/A/CommonAuth
0x00000001f26f5000 	/System/Library/Frameworks/AVFAudio.framework/Versions/A/AVFAudio
0x00000001a5cff000 	/System/Library/PrivateFrameworks/AXCoreUtilities.framework/Versions/A/AXCoreUtilities
0x0000000196386000 	/System/Library/PrivateFrameworks/AudioSession.framework/Versions/A/AudioSession
0x0000000197e31000 	/System/Library/Frameworks/IOBluetooth.framework/Versions/A/IOBluetooth
0x000000019451b000 	/System/Library/PrivateFrameworks/MediaExperience.framework/Versions/A/MediaExperience
0x00000001961c9000 	/System/Library/PrivateFrameworks/AudioSession.framework/libSessionUtility.dylib
0x000000019683f000 	/System/Library/PrivateFrameworks/AudioResourceArbitration.framework/Versions/A/AudioResourceArbitration
0x000000019aed5000 	/System/Library/PrivateFrameworks/PowerLog.framework/Versions/A/PowerLog
0x000000019adf8000 	/System/Library/Frameworks/CoreBluetooth.framework/Versions/A/CoreBluetooth
0x000000019df7c000 	/System/Library/Frameworks/AudioUnit.framework/Versions/A/AudioUnit
0x0000000191e76000 	/System/Library/PrivateFrameworks/CoreUtils.framework/Versions/A/CoreUtils
0x0000000200012000 	/System/Library/PrivateFrameworks/CoreUtilsExtras.framework/Versions/A/CoreUtilsExtras
0x000000020a2d8000 	/System/Library/PrivateFrameworks/IO80211.framework/Versions/A/IO80211
0x00000001a5d1a000 	/usr/lib/libAccessibility.dylib
0x000000019a4f7000 	/System/Library/Frameworks/MediaAccessibility.framework/Versions/A/MediaAccessibility
0x000000019ba7e000 	/System/Library/PrivateFrameworks/FrontBoardServices.framework/Versions/A/FrontBoardServices
0x000000019d559000 	/System/Library/PrivateFrameworks/BackBoardServices.framework/Versions/A/BackBoardServices
0x000000019bb38000 	/System/Library/PrivateFrameworks/BoardServices.framework/Versions/A/BoardServices
0x0000000194482000 	/System/Library/PrivateFrameworks/IconFoundation.framework/Versions/A/IconFoundation
0x0000000199d8f000 	/System/Library/PrivateFrameworks/SpeechRecognitionCore.framework/Versions/A/SpeechRecognitionCore
0x00000001c9d46000 	/System/Library/Frameworks/OSLog.framework/Versions/A/OSLog
0x00000001ab113000 	/System/Library/PrivateFrameworks/LoggingSupport.framework/Versions/A/LoggingSupport
0x0000000103d90000 	/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/server/libjvm.dylib
0x0000000102bf4000 	/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/libverify.dylib
0x0000000102c48000 	/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/libjava.dylib
0x0000000102c84000 	/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/libinstrument.dylib
0x0000000102c24000 	/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/libzip.dylib
0x000000011c4c0000 	/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/libnet.dylib
0x000000011c620000 	/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/libnio.dylib
0x000000011c6b8000 	/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/libawt.dylib
0x000000011d000000 	/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/libmlib_image.dylib
0x00000001f4544000 	/System/Library/Frameworks/JavaRuntimeSupport.framework/Versions/A/JavaRuntimeSupport
0x00000001a9eaa000 	/System/Library/Frameworks/Carbon.framework/Versions/A/Carbon
0x00000001a2092000 	/System/Library/Frameworks/Carbon.framework/Versions/A/Frameworks/CommonPanels.framework/Versions/A/CommonPanels
0x000000019d891000 	/System/Library/Frameworks/Carbon.framework/Versions/A/Frameworks/Help.framework/Versions/A/Help
0x00000001a2096000 	/System/Library/Frameworks/Carbon.framework/Versions/A/Frameworks/ImageCapture.framework/Versions/A/ImageCapture
0x00000001a206a000 	/System/Library/Frameworks/Carbon.framework/Versions/A/Frameworks/OpenScripting.framework/Versions/A/OpenScripting
0x00000001a208e000 	/System/Library/Frameworks/Carbon.framework/Versions/A/Frameworks/Ink.framework/Versions/A/Ink
0x00000001a208a000 	/System/Library/Frameworks/Carbon.framework/Versions/A/Frameworks/SecurityHI.framework/Versions/A/SecurityHI
0x000000011d18c000 	/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/libawt_lwawt.dylib
0x000000011c668000 	/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/libosxapp.dylib
0x00000001a8ee1000 	/System/Library/Frameworks/ExceptionHandling.framework/Versions/A/ExceptionHandling
0x000000011d098000 	/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/liblcms.dylib
0x000000011d0f4000 	/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/libfontmanager.dylib
0x000000019b11a000 	/System/Library/Frameworks/FileProvider.framework/Versions/A/FileProvider
0x000000018d17a000 	/System/Library/Frameworks/Accounts.framework/Versions/A/Accounts
0x000000019b2c6000 	/System/Library/PrivateFrameworks/GenerationalStorage.framework/Versions/A/GenerationalStorage
0x000000019aec9000 	/System/Library/PrivateFrameworks/SymptomDiagnosticReporter.framework/Versions/A/SymptomDiagnosticReporter
0x000000018b082000 	/System/Library/PrivateFrameworks/DesktopServicesPriv.framework/Versions/A/DesktopServicesPriv
0x0000000189b34000 	/usr/lib/libsandbox.1.dylib
0x000000019681a000 	/System/Library/PrivateFrameworks/NetAuth.framework/Versions/A/NetAuth
0x0000000197a92000 	/System/Library/PrivateFrameworks/Sharing.framework/Versions/A/Sharing
0x000000018ed42000 	/System/Library/PrivateFrameworks/login.framework/Versions/A/Frameworks/loginsupport.framework/Versions/A/loginsupport
0x000000019d392000 	/System/Library/PrivateFrameworks/Apple80211.framework/Versions/A/Apple80211
0x0000000192817000 	/System/Library/PrivateFrameworks/AuthKit.framework/Versions/A/AuthKit
0x0000000191df9000 	/System/Library/Frameworks/CoreWLAN.framework/Versions/A/CoreWLAN
0x00000001ab1ca000 	/usr/lib/swift/libswiftCoreAudio.dylib
0x00000001b0338000 	/usr/lib/swift/libswiftCoreLocation.dylib
0x00000001941e4000 	/usr/lib/libIOReport.dylib
0x000000019d396000 	/System/Library/PrivateFrameworks/CoreWiFi.framework/Versions/A/CoreWiFi
0x00000001d0b0a000 	/System/Library/PrivateFrameworks/WiFiPeerToPeer.framework/Versions/A/WiFiPeerToPeer
0x00000001929b2000 	/System/Library/Frameworks/UserNotifications.framework/Versions/A/UserNotifications
0x0000000196d4b000 	/System/Library/PrivateFrameworks/CorePhoneNumbers.framework/Versions/A/CorePhoneNumbers
0x00000001964f6000 	/System/Library/PrivateFrameworks/MultiverseSupport.framework/Versions/A/MultiverseSupport
0x000000019d7e7000 	/System/Library/PrivateFrameworks/RemoteServiceDiscovery.framework/Versions/A/RemoteServiceDiscovery
0x000000019aff6000 	/System/Library/PrivateFrameworks/DiskManagement.framework/Versions/A/DiskManagement
0x000000019aefd000 	/System/Library/PrivateFrameworks/AppleIDAuthSupport.framework/Versions/A/AppleIDAuthSupport
0x00000001f8af4000 	/System/Library/PrivateFrameworks/AAAFoundation.framework/Versions/A/AAAFoundation
0x0000000196d1e000 	/System/Library/PrivateFrameworks/KeychainCircle.framework/Versions/A/KeychainCircle
0x000000019d7fb000 	/System/Library/PrivateFrameworks/RemoteXPC.framework/Versions/A/RemoteXPC
0x000000019d89d000 	/usr/lib/libcsfde.dylib
0x000000019438c000 	/usr/lib/libCoreStorage.dylib
0x000000019afc2000 	/System/Library/PrivateFrameworks/MediaKit.framework/Versions/A/MediaKit
0x000000019af0a000 	/System/Library/Frameworks/DiscRecording.framework/Versions/A/DiscRecording
0x0000000197f10000 	/System/Library/PrivateFrameworks/ProtectedCloudStorage.framework/Versions/A/ProtectedCloudStorage
0x000000019d895000 	/System/Library/PrivateFrameworks/EFILogin.framework/Versions/A/EFILogin
0x000000019b2ee000 	/System/Library/PrivateFrameworks/OctagonTrust.framework/Versions/A/OctagonTrust
0x0000000190323000 	/System/Library/Frameworks/CoreLocation.framework/Versions/A/CoreLocation
0x00000001981cb000 	/System/Library/PrivateFrameworks/GeoServices.framework/Versions/A/GeoServices
0x0000000196193000 	/System/Library/PrivateFrameworks/LocationSupport.framework/Versions/A/LocationSupport
0x000000020723e000 	/System/Library/PrivateFrameworks/GeoServicesCore.framework/Versions/A/GeoServicesCore
0x00000001a5cf4000 	/System/Library/PrivateFrameworks/PhoneNumbers.framework/Versions/A/PhoneNumbers
0x0000000194384000 	/usr/lib/libMatch.1.dylib
0x00000001b3b13000 	/System/Library/CoreServices/RawCamera.bundle/Contents/MacOS/RawCamera
0x0000000196c21000 	/System/Library/PrivateFrameworks/MobileAsset.framework/Versions/A/MobileAsset
0x00000001fa842000 	/System/Library/PrivateFrameworks/AppleJPEGXL.framework/Versions/A/AppleJPEGXL
0x00000001b0c94000 	/System/Library/PrivateFrameworks/SoftwareUpdateCoreSupport.framework/Versions/A/SoftwareUpdateCoreSupport
0x00000001b800b000 	/System/Library/PrivateFrameworks/SoftwareUpdateCoreConnect.framework/Versions/A/SoftwareUpdateCoreConnect
0x000000019dfa2000 	/System/Library/PrivateFrameworks/StreamingZip.framework/Versions/A/StreamingZip
0x00000001b0949000 	/System/Library/PrivateFrameworks/MSUDataAccessor.framework/Versions/A/MSUDataAccessor
0x00000001acc0e000 	/usr/lib/libbootpolicy.dylib
0x00000001bea4e000 	/usr/lib/libpartition2_dynamic.dylib
0x00000001fbe2a000 	/System/Library/PrivateFrameworks/CMPhoto.framework/Versions/A/CMPhoto
0x0000000195295000 	/System/Library/Frameworks/MediaToolbox.framework/Versions/A/MediaToolbox
0x000000019b0e6000 	/System/Library/PrivateFrameworks/CoreAVCHD.framework/Versions/A/CoreAVCHD
0x000000019b0e2000 	/System/Library/PrivateFrameworks/Mangrove.framework/Versions/A/Mangrove
0x000000019b6ef000 	/System/Library/Frameworks/CoreTelephony.framework/Versions/A/CoreTelephony
0x000000019b0d6000 	/System/Library/PrivateFrameworks/CoreAUC.framework/Versions/A/CoreAUC
0x0000000196d55000 	/usr/lib/libTelephonyUtilDynamic.dylib
0x00000001b031e000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vImage.framework/Versions/A/Libraries/libCGInterfaces.dylib
0x000000011ea4c000 	/usr/lib/libobjc-trampolines.dylib
0x0000000225e8b000 	/usr/lib/libAccessibilityBaseImplementations.dylib
0x0000000194718000 	/System/Library/PrivateFrameworks/login.framework/Versions/A/login
0x000000019bc51000 	/System/Library/Frameworks/LocalAuthentication.framework/Versions/A/LocalAuthentication
0x000000020c10e000 	/System/Library/PrivateFrameworks/LocalAuthenticationCore.framework/Versions/A/LocalAuthenticationCore
0x000000019bc9e000 	/System/Library/Frameworks/LocalAuthentication.framework/Support/SharedUtils.framework/Versions/A/SharedUtils
0x000000019bbec000 	/System/Library/Frameworks/CryptoTokenKit.framework/Versions/A/CryptoTokenKit
0x000000018ea25000 	/System/Library/PrivateFrameworks/ViewBridge.framework/Versions/A/ViewBridge
0x00000001eed02000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Resources/GLEngine.bundle/GLEngine
0x00000001eeb9a000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGLProgrammability.dylib
0x0000000137d40000 	/System/Library/Extensions/AppleMetalOpenGLRenderer.bundle/Contents/MacOS/AppleMetalOpenGLRenderer
0x00000001eee62000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Resources/GLRendererFloat.bundle/GLRendererFloat
0x00000001efe32000 	/System/Library/Extensions/AGXMetalG14X.bundle/Contents/MacOS/AGXMetalG14X
0x00000001a4c4b000 	/System/Library/PrivateFrameworks/IOGPU.framework/Versions/A/IOGPU
0x0000000137f58000 	/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/libdcpr.dylib
0x0000000137f8c000 	/Users/<USER>/Library/Caches/JNA/temp/jna8644067996022288175.tmp
0x00000001736b4000 	/opt/homebrew/Cellar/tesseract/5.5.0/lib/libtesseract.5.dylib
0x000000017395c000 	/opt/homebrew/Cellar/leptonica/1.85.0/lib/libleptonica.6.dylib
0x0000000170e18000 	/opt/homebrew/Cellar/libarchive/3.7.7/lib/libarchive.13.dylib
0x0000000137fb4000 	/opt/homebrew/Cellar/libpng/1.6.46/lib/libpng16.16.dylib
0x00000001691bc000 	/opt/homebrew/Cellar/jpeg-turbo/3.1.0/lib/libjpeg.8.3.2.dylib
0x0000000137d10000 	/opt/homebrew/Cellar/giflib/5.2.2/lib/libgif.7.2.0.dylib
0x0000000170f48000 	/opt/homebrew/Cellar/libtiff/4.7.0/lib/libtiff.6.dylib
0x0000000170ec4000 	/opt/homebrew/Cellar/webp/1.5.0/lib/libwebp.7.1.10.dylib
0x0000000137d24000 	/opt/homebrew/Cellar/webp/1.5.0/lib/libwebpmux.3.1.1.dylib
0x0000000171f64000 	/opt/homebrew/Cellar/openjpeg/2.5.3/lib/libopenjp2.2.5.3.dylib
0x00000001734b0000 	/opt/homebrew/Cellar/zstd/1.5.6/lib/libzstd.1.5.6.dylib
0x0000000169178000 	/opt/homebrew/Cellar/xz/5.6.4/lib/liblzma.5.dylib
0x0000000137fec000 	/opt/homebrew/Cellar/webp/1.5.0/lib/libsharpyuv.0.1.1.dylib
0x0000000170f1c000 	/opt/homebrew/Cellar/lz4/1.10.0/lib/liblz4.1.10.0.dylib
0x0000000169160000 	/opt/homebrew/Cellar/libb2/0.98.1/lib/libb2.1.dylib
0x000000019d8a9000 	/usr/lib/libcurl.4.dylib
0x0000000226509000 	/usr/lib/libcrypto.46.dylib
0x00000002271b7000 	/usr/lib/libssl.48.dylib
0x000000019d57d000 	/System/Library/Frameworks/LDAP.framework/Versions/A/LDAP
0x000000019d5b9000 	/System/Library/PrivateFrameworks/TrustEvaluationAgent.framework/Versions/A/TrustEvaluationAgent
0x00000001964e2000 	/usr/lib/libsasl2.2.dylib

VM Arguments:
jvm_args: -Dvisualvm.id=2835993367126083 -javaagent:/Applications/IntelliJ IDEA CE.app/Contents/lib/idea_rt.jar=63669:/Applications/IntelliJ IDEA CE.app/Contents/bin -Dfile.encoding=UTF-8 
java_command: net.qiyuesuo.pdfbox.PdfToTextWithOCR
java_class_path (initial): /Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/charsets.jar:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/deploy.jar:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/cldrdata.jar:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/dnsns.jar:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/jaccess.jar:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/jfxrt.jar:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/localedata.jar:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/nashorn.jar:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/sunec.jar:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/sunjce_provider.jar:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/sunpkcs11.jar:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/zipfs.jar:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/javaws.jar:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/jce.jar:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/jfr.jar:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/jfxswt.jar:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/jsse.jar:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/management-agent.jar:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/plugin.jar:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/resources.jar:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/rt.jar:/Users/<USER>/1source/opensource/qystest/target/classes:/Users/<USER>/2devsoft/apache-maven-3.6.0/mavenRepository/org/springframework/boot/spring-boot-starter/2.7.18/spring-boot-starter-2.7.18.jar:/Users/<USER>/2devsoft/apache-maven-3.6.0/mavenRepositor
Launcher Type: SUN_STANDARD

Environment Variables:
PATH=/Users/<USER>/.yarn/bin:/Users/<USER>/.config/yarn/global/node_modules/.bin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin
SHELL=/bin/bash

Signal Handlers:
SIGSEGV: [libjvm.dylib+0x55c4b0], sa_mask[0]=11111111011111110111111111111111, sa_flags=SA_ONSTACK|SA_RESTART|SA_SIGINFO
SIGBUS: [libjvm.dylib+0x55c4b0], sa_mask[0]=11111111011111110111111111111111, sa_flags=SA_RESTART|SA_SIGINFO
SIGFPE: [libjvm.dylib+0x55c4b0], sa_mask[0]=11111111011111110111111111111111, sa_flags=SA_RESTART|SA_SIGINFO
SIGPIPE: [libjvm.dylib+0x459a18], sa_mask[0]=11111111011111110111111111111111, sa_flags=SA_RESTART|SA_SIGINFO
SIGXFSZ: [libjvm.dylib+0x459a18], sa_mask[0]=11111111011111110111111111111111, sa_flags=SA_RESTART|SA_SIGINFO
SIGILL: [libjvm.dylib+0x55c4b0], sa_mask[0]=11111111011111110111111111111111, sa_flags=SA_RESTART|SA_SIGINFO
SIGUSR1: SIG_DFL, sa_mask[0]=00000000000000000000000000000000, sa_flags=none
SIGUSR2: [libjvm.dylib+0x45a064], sa_mask[0]=00100000000000000000000000000000, sa_flags=SA_RESTART|SA_SIGINFO
SIGHUP: [libjvm.dylib+0x45805c], sa_mask[0]=11111111011111110111111111111111, sa_flags=SA_RESTART|SA_SIGINFO
SIGINT: [libjvm.dylib+0x45805c], sa_mask[0]=11111111011111110111111111111111, sa_flags=SA_RESTART|SA_SIGINFO
SIGTERM: [libjvm.dylib+0x45805c], sa_mask[0]=11111111011111110111111111111111, sa_flags=SA_RESTART|SA_SIGINFO
SIGQUIT: [libjvm.dylib+0x45805c], sa_mask[0]=11111111011111110111111111111111, sa_flags=SA_RESTART|SA_SIGINFO


---------------  S Y S T E M  ---------------

OS:Bsduname:Darwin 23.3.0 Darwin Kernel Version 23.3.0: Wed Dec 20 21:31:00 PST 2023; root:xnu-10002.81.5~7/RELEASE_ARM64_T6020 arm64
rlimit: STACK 8176k, CORE 0k, NPROC 5333, NOFILE 12544, AS infinity
load average:5.77 4.30 3.36

CPU:total 12 (initial active 12) 

Memory: 16k page, physical 33554432k(70240k free)

/proc/meminfo:


vm_info: Java HotSpot(TM) 64-Bit Server VM (25.401-b10) for bsd-aarch64 JRE (1.8.0_401-b10), built on Dec 19 2023 12:41:02 by "java_re" with gcc Apple LLVM 12.0.0 (clang-1200.0.32.29)

time: Thu Feb 20 17:30:02 2025
timezone: CST
elapsed time: 0.740424 seconds (0d 0h 0m 0s)

