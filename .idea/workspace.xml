<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="9cb74793-3905-4ed1-a22d-11f0c753f432" name="更改" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Interface" />
        <option value="JUnit4 Test Class" />
        <option value="Enum" />
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="jar://$USER_HOME$/2devsoft/apache-maven-3.6.0/mavenRepository/com/squareup/okhttp3/okhttp/3.14.9/okhttp-3.14.9.jar!/okhttp3/ConnectionPool.class" root0="SKIP_INSPECTION" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="alwaysUpdateSnapshots" value="true" />
        <option name="customMavenHome" value="$USER_HOME$/2devsoft/apache-maven-3.6.0" />
        <option name="localRepository" value="$USER_HOME$/2devsoft/apache-maven-3.6.0/mavenRepository" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="$USER_HOME$/2devsoft/apache-maven-3.6.0/conf/settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="MavenRunner">
    <option name="skipTests" value="true" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 3
}</component>
  <component name="ProjectId" id="2PzUfWxYI8uuKH6XjGKYWgxTNx2" />
  <component name="ProjectViewState">
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Application.AsposeTextExtractor.executor": "Debug",
    "Application.ChatCompletionsExample.executor": "Run",
    "Application.ChinesePDFToMarkdown (1).executor": "Run",
    "Application.ChinesePDFToMarkdown.executor": "Debug",
    "Application.EncryptUtils.executor": "Debug",
    "Application.FileHashCalculator.executor": "Run",
    "Application.HelloWorld.executor": "Run",
    "Application.PDFBoxTextExtractor.executor": "Run",
    "Application.PDFTextReplacer (1).executor": "Run",
    "Application.PDFTextReplacer.executor": "Run",
    "Application.PDFToMarkdownConverter3 (1).executor": "Run",
    "Application.PDFToMarkdownConverter3 (2).executor": "Run",
    "Application.PDFToMarkdownConverter3.executor": "Debug",
    "Application.PPOcrDemo.executor": "Run",
    "Application.PPToPdfConverter.executor": "Run",
    "Application.PdfToMarkdownConverter.executor": "Run",
    "Application.PdfToMarkdownConverter2.executor": "Run",
    "Application.PdfToTextWithOCR.executor": "Run",
    "Application.PrintTest.executor": "Run",
    "Application.TesseractOcr.executor": "Run",
    "Application.WebApplication.executor": "Run",
    "Application.WordContentExtractor (1).executor": "Debug",
    "Application.WordContentExtractor (2).executor": "Debug",
    "Application.WordContentExtractor.executor": "Run",
    "Application.WordPageExtractor.executor": "Run",
    "Application.WordToPdfConverter.executor": "Run",
    "Application.net.qiyuesuo.WebApplication.executor": "Run",
    "JUnit.PdfCheckUtilsTest.executor": "Run",
    "JUnit.PdfCheckUtilsTest.log.executor": "Debug",
    "JUnit.PdfCheckUtilsTest.testCheckPdf.executor": "Debug",
    "Maven.Word-Text-Extraction [clean].executor": "Run",
    "Maven.Word-Text-Extraction [install].executor": "Run",
    "Maven.qystest [clean].executor": "Run",
    "Maven.qystest [install].executor": "Run",
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.OpenProjectViewOnStart": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "/Users/<USER>/1source/opensource/qystest/src/main/java/net/qiyuesuo/pdfbox",
    "project.structure.last.edited": "SDKs",
    "project.structure.proportion": "0.15",
    "project.structure.side.proportion": "0.40114942",
    "settings.editor.selected.configurable": "configurable.group.build"
  }
}]]></component>
  <component name="RebelAgentSelection">
    <selection>jr</selection>
  </component>
  <component name="RecentsManager">
    <key name="CreateClassDialog.RecentsKey">
      <recent name="net.qiyuesuo.extraction.config" />
    </key>
    <key name="CopyFile.RECENT_KEYS">
      <recent name="$PROJECT_DIR$/src/main/java/net/qiyuesuo/pdfbox" />
      <recent name="$PROJECT_DIR$/src/main/resources/onnx" />
      <recent name="$PROJECT_DIR$/src/test/resources/file" />
      <recent name="$PROJECT_DIR$/src/test/resources/pic" />
      <recent name="$PROJECT_DIR$/src/main/resources/tessdata" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="$PROJECT_DIR$/src/main/resources/onnx" />
      <recent name="$PROJECT_DIR$/src/main/resources/tessdata" />
      <recent name="$PROJECT_DIR$" />
      <recent name="$PROJECT_DIR$/src/test/resources/resources" />
      <recent name="$PROJECT_DIR$/src/main/resources/file" />
    </key>
    <key name="CreateTestDialog.Recents.Supers">
      <recent name="" />
    </key>
    <key name="MoveClassesOrPackagesDialog.RECENTS_KEY">
      <recent name="net" />
    </key>
    <key name="CreateTestDialog.RecentsKey">
      <recent name="net.qiyuesuo.extraction.service" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="net.qiyuesuo.pdfbox" />
      <recent name="net.qiyuesuo.deepseek" />
      <recent name="net.qiyuesuo.print" />
      <recent name="net.qiyuesuo.pdf" />
      <recent name="net.qiyuesuo.controller" />
    </key>
  </component>
  <component name="RunManager" selected="Application.PDFTextReplacer (1)">
    <configuration default="true" type="ArquillianJUnit" factoryName="" nameIsGenerated="true">
      <option name="arquillianRunConfiguration">
        <value>
          <option name="containerStateName" value="" />
        </value>
      </option>
      <option name="TEST_OBJECT" value="class" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="EncryptUtils" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="net.qiyuesuo.storage.EncryptUtils" />
      <module name="qystest" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="net.qiyuesuo.storage.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="PDFTextReplacer (1)" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="net.qiyuesuo.pdfbox.PDFTextReplacer" />
      <module name="qystest" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="net.qiyuesuo.pdfbox.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="PDFTextReplacer" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="net.qiyuesuo.pdfbox.PDFTextReplacer" />
      <module name="qystest" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="net.qiyuesuo.pdfbox.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="PPOcrDemo" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="net.qiyuesuo.ocr.PPOcrDemo" />
      <module name="qystest" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="net.qiyuesuo.ocr.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="TesseractOcr" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="net.qiyuesuo.pdfbox.TesseractOcr" />
      <module name="qystest" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="net.qiyuesuo.pdfbox.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <list>
      <item itemvalue="Application.PDFTextReplacer (1)" />
      <item itemvalue="Application.PDFTextReplacer" />
      <item itemvalue="Application.EncryptUtils" />
      <item itemvalue="Application.PPOcrDemo" />
      <item itemvalue="Application.TesseractOcr" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="Application.PDFTextReplacer (1)" />
        <item itemvalue="Application.PDFTextReplacer" />
        <item itemvalue="Application.EncryptUtils" />
        <item itemvalue="Application.PPOcrDemo" />
        <item itemvalue="Application.TesseractOcr" />
      </list>
    </recent_temporary>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="9cb74793-3905-4ed1-a22d-11f0c753f432" name="更改" comment="" />
      <created>1684459508008</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1684459508008</updated>
      <workItem from="1684459509041" duration="29273000" />
      <workItem from="1684749376414" duration="1645000" />
      <workItem from="1684805256208" duration="24043000" />
      <workItem from="1684831344036" duration="16491000" />
      <workItem from="1684903564175" duration="21358000" />
      <workItem from="1684976429004" duration="1047000" />
      <workItem from="1684977519908" duration="669000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/net/qiyuesuo/storage/EncryptUtils.java</url>
          <line>177</line>
          <option name="timeStamp" value="1" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/net/qiyuesuo/storage/EncryptUtils.java</url>
          <line>227</line>
          <option name="timeStamp" value="5" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/net/qiyuesuo/storage/EncryptUtils.java</url>
          <line>218</line>
          <option name="timeStamp" value="10" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/net/qiyuesuo/storage/EncryptUtils.java</url>
          <line>212</line>
          <option name="timeStamp" value="11" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/net/qiyuesuo/storage/EncryptUtils.java</url>
          <line>184</line>
          <option name="timeStamp" value="14" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/net/qiyuesuo/pdfbox/PDFTextReplacer.java</url>
          <line>106</line>
          <option name="timeStamp" value="15" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>