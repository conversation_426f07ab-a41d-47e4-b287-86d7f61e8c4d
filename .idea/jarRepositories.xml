<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="RemoteRepositoriesConfiguration">
    <remote-repository>
      <option name="id" value="com.e-iceblue" />
      <option name="name" value="e-iceblue" />
      <option name="url" value="https://repo.e-iceblue.com/nexus/content/groups/public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="com.e-iceblue" />
      <option name="name" value="e-iceblue" />
      <option name="url" value="https://maven.aliyun.com/repository/public" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="Central Repository" />
      <option name="url" value="https://repo.maven.apache.org/maven2" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="AsposeJavaAPI" />
      <option name="name" value="Aspose Java API" />
      <option name="url" value="http://nexus.qiyuesuo.net/repository/maven-public" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="Maven Central repository" />
      <option name="url" value="https://repo1.maven.org/maven2" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="aliyun" />
      <option name="name" value="aliyun" />
      <option name="url" value="https://maven.aliyun.com/repository/public" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="Central Repository" />
      <option name="url" value="https://maven.aliyun.com/repository/public" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="Central Repository" />
      <option name="url" value="http://nexus.qiyuesuo.net/repository/maven-public" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="Central Repository" />
      <option name="url" value="https://nexus.qiyuesuo.me/repository/maven-public" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="aliyun" />
      <option name="name" value="aliyun" />
      <option name="url" value="https://nexus.qiyuesuo.me/repository/maven-public" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="com.e-iceblue" />
      <option name="name" value="e-iceblue" />
      <option name="url" value="https://nexus.qiyuesuo.me/repository/maven-public" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="com.e-iceblue" />
      <option name="name" value="e-iceblue" />
      <option name="url" value="http://nexus.qiyuesuo.net/repository/maven-public" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="com.e-iceblue" />
      <option name="name" value="e-iceblue" />
      <option name="url" value="http://nexus.qiyuesuo.me/repository/maven-public" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="repo-qiyuesuo-me" />
      <option name="name" value="契约锁仓库" />
      <option name="url" value="https://maven.aliyun.com/repository/public" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="Central Repository" />
      <option name="url" value="http://nexus.qiyuesuo.me/repository/maven-public" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="com.e-iceblue" />
      <option name="name" value="e-iceblue" />
      <option name="url" value="http://repo.e-iceblue.com/nexus/content/groups/public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="AsposeJavaAPI" />
      <option name="name" value="Aspose Java API" />
      <option name="url" value="https://releases.aspose.com/java/repo/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="jboss.community" />
      <option name="name" value="JBoss Community repository" />
      <option name="url" value="https://repository.jboss.org/nexus/content/repositories/public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="repo-qiyuesuo-me" />
      <option name="name" value="契约锁仓库" />
      <option name="url" value="https://nexus.qiyuesuo.me/repository/maven-public" />
    </remote-repository>
  </component>
</project>