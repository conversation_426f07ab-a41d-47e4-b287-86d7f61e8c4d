package net.qiyuesuo.pdf;

import net.qiyuesuo.common.pdf.position.KeywordPosition;
import net.qiyuesuo.common.pdf.position.KeywordSearchException;
import net.qiyuesuo.common.pdf.position.KeywordSearchOptions;
import net.qiyuesuo.common.pdf.position.NewPdfboxKeywordPositionProvider;
import net.qiyuesuo.common.pdf.position.PdfKeywordPositionUtils;
import net.qiyuesuo.common.pdf.position.PdfKeywordUtils;
import net.qiyuesuo.common.pdf.position.PdfboxKeywordPositionProvider;
import org.junit.Test;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class PdfKeywordUtilsTest {


	/**
	 *	Itext查询关键字（模板参数定位、敏感词查询使用）
	 */
	@Test
	public void getKeyWordPositionItext7() throws IOException {
		File pdf = new File("C:\\Users\\<USER>\\Desktop\\01小故事-今天我和你要签订一个“电子合同”-v2.0.pdf");

		List<String> keyWords = new ArrayList<>();
		keyWords.add("电子签");
		keyWords.add("机构");
		//keyWords.add("了！这下");

		Map<String, ArrayList<KeywordPosition>> textPosition = PdfKeywordPositionUtils.getTextPosition(pdf, keyWords);

		for (Map.Entry<String, ArrayList<KeywordPosition>> entry : textPosition.entrySet()) {
			System.out.println(entry.getKey() + "----" + entry.getValue());
		}
	}


	/**
	 *	Itext关键字查询
	 */
	@Test
	public void getKeyWordPositionItext5() throws KeywordSearchException {
		File pdf = new File("C:\\Users\\<USER>\\Desktop\\1343-图纸_S00007T-T044_首层建筑平面图3_0_a0 heng(1).pdf");

		Set<String> keyWords = new HashSet<>();
		keyWords.add("牛文杰");
		keyWords.add("机构");

		KeywordSearchOptions options = new KeywordSearchOptions();
		options.setKeywords(keyWords);
		options.setIgnoreUnusualWord(true);

		Map<String, ArrayList<KeywordPosition>> queryKeyword = PdfKeywordUtils.queryKeyword(pdf, options);
		System.out.println();
	}


	/**
	 *	Pdfbox关键字查询
	 */
	@Test
	public void pdfBoxTest() throws KeywordSearchException {
		File pdf = new File("C:\\Users\\<USER>\\Desktop\\1343-图纸_S00007T-T044_首层建筑平面图3_0_a0 heng(1).pdf");

		Set<String> keyWords = new HashSet<>();
		keyWords.add("牛文杰");

		KeywordSearchOptions options = new KeywordSearchOptions();
		options.setKeywords(keyWords);
		options.setIgnoreUnusualWord(true);

		PdfboxKeywordPositionProvider pdfboxKeywordPositionProvider = new PdfboxKeywordPositionProvider();
		Map<String, ArrayList<KeywordPosition>> stringArrayListMap = pdfboxKeywordPositionProvider.queryKeyword(pdf, options);
		System.out.println();
	}


	/**
	 *	新PDFbox关键字查询
	 */
	@Test
	public void newPdfBoxTest() throws KeywordSearchException {
		File pdf = new File("C:\\Users\\<USER>\\Desktop\\办公区检测报告测试.pdf");

		Set<String> keyWords = new HashSet<>();
		//keyWords.add("签发人");
		keyWords.add("签发人签字");
		//keyWords.add("雷电防护装置");

		KeywordSearchOptions options = new KeywordSearchOptions();
		options.setKeywords(keyWords);
		options.setIgnoreNewline(true);
		options.setIgnoreNewpage(true);
		options.setPosition(KeywordSearchOptions.PositionType.LEFT_BOTTOM);

		NewPdfboxKeywordPositionProvider newPdfboxKeywordPositionProvider = new NewPdfboxKeywordPositionProvider();
		Map<String, ArrayList<KeywordPosition>> stringArrayListMap = newPdfboxKeywordPositionProvider.queryKeyword(pdf, options);
		System.out.println();

	}

}
