package net.qiyuesuo.pdf;

import lombok.extern.slf4j.Slf4j;

import net.qiyuesuo.common.pdf.formatCheck.PdfCheckUtils;
import net.qiyuesuo.common.pdf.formatCheck.handler.AbstractCheckHandler;
import net.qiyuesuo.common.pdf.formatCheck.handler.exception.CheckException;
import net.qiyuesuo.common.pdf.formatCheck.model.EnumPartialModel;
import net.qiyuesuo.common.pdf.image.monitor.model.ToPdfTask;
import net.qiyuesuo.common.word.FileFormatUtils;
import org.junit.Before;
import org.junit.Test;

import java.io.File;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Paths;

/**
 * 测试对pdf文件进行格式校验
 * 疑问点：同一个文件从契约锁网页上传，经过PdfCheckUtils.checkPdf方法时能检测出问题，但是通过以下单元测试方法上传时却检测不出问题
 *
 */
@Slf4j
public class PdfCheckUtilsTest {
	File file = null;
	String filePath = "";

	@Before
	public void setUp() throws Exception {
		// 疑问：同样的文件调用同样的方法，从契约锁前端页面上传时异常。这里直接调用是好的？
		filePath = "/Users/<USER>/Downloads/INTERNAL-2178/response（itextpdf解析报错Illegal pages tree，页节点存在循环依赖的问题）.pdf";
		file = new File(filePath);
	}

	@Test
	public void testCheckPdf() throws Exception {
		ToPdfTask toPdfTask = new ToPdfTask();
//		toPdfTask.setPdfReaderPartial(EnumPartialModel.OPT_PARTIAL.getValue());
		toPdfTask.setToEncryptPermissionsCheck(true);
		toPdfTask.setToOnlyEncryptCheck(false);
		toPdfTask.setToAdobeSignedCheck(false);
		try {
			// 如果抛出CheckException异常，说明检测格式时发生了异常
//			PdfCheckUtils.checkPdf(file, toPdfTask);

			byte[] fileByte = Files.readAllBytes(Paths.get(filePath));
			PdfCheckUtils.checkPdf(fileByte, toPdfTask);
			log.error("sucess");
		} catch (CheckException e) {
			// 如果在构建itextpdf的PdfReader时报的异常，则尝试修正，否则直接抛出异常信息
			if (net.qiyuesuo.common.pdf.formatCheck.model.ErrorCodes.DOCUMENT_PDFREADER_ERR.getType().equals(e.getType())) {
				//itext无法正常读取，则格式化pdf在尝试
				try {
					file = FileFormatUtils.formatFile(file, "pdf");
				} catch (Exception ex) {
					log.error(AbstractCheckHandler.CHECK_ERR_MESSAGE);
				}
				try {
					PdfCheckUtils.checkPdf(file, toPdfTask);
				} catch (CheckException throwable) {
					log.error("尝试修复后，检查格式发现异常", throwable.getMessage());
				} catch (Throwable ex) {
					log.error("尝试修复后，检查格式发现异常2", ex);
				}
			} else {
				log.error(e.getMessage());
			}
		} catch (Throwable ex) {
			log.error("检查格式发现异常1", ex);
		}
	}
}
