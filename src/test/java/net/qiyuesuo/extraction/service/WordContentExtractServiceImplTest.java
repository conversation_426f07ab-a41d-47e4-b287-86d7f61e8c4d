package net.qiyuesuo.extraction.service;

import lombok.extern.slf4j.Slf4j;
import net.qiyuesuo.extraction.config.ExtractOption;
import net.qiyuesuo.extraction.config.ExtractResult;
import net.qiyuesuo.extraction.config.enums.ExtractConfigEnum;
import net.qiyuesuo.extraction.config.properties.ExtractorProperties;
import org.junit.Before;
import org.junit.Test;

import java.io.File;
import java.net.URL;
import java.nio.file.Files;
import java.util.Arrays;
import java.util.List;

@Slf4j
public class WordContentExtractServiceImplTest {

    ContentExtractService contentExtractService;

    @Before
    public void setUp() throws Exception {
        // 使用类库
        ExtractConfigEnum type = ExtractConfigEnum.ASPOSE;

        ExtractorProperties properties = new ExtractorProperties(type);
        contentExtractService = new ContentExtractServiceImpl(properties);
    }

    /**
     * 提取多页多项内容
     *
     * @throws Exception
     */
    @Test
    public void testAllPageInput() throws Exception {
        List<Integer> pageNumbers = Arrays.asList(1, 3, 5, 7);

        URL resource = ClassLoader.getSystemResource("./file/example.docx");
        File file = new File(resource.toURI());
        ExtractOption option = ExtractOption.builder()
                .bytes(Files.readAllBytes(file.toPath()))
                .pageNumbers(pageNumbers)
                .body(true)
                .header(true)
                .footer(true)
                .build();
        ExtractResult result = contentExtractService.getContent(option);

        pageNumbers.forEach(pageNo ->{
            String bodyText = result.getBodyTextFromPageNo(pageNo);
            String headerText = result.getHeaderTextFromPageNo(pageNo);
            String footerText = result.getFooterTextFromPageNo(pageNo);

            log.info("\n>>>>>>>>>>>>>>> 第{}页bodyText <<<<<<<<<<<<<<<\n{}", pageNo, bodyText);
            log.info("\n>>>>>>>>>>>>>>> 第{}页headerText <<<<<<<<<<<<<<<\n{}", pageNo, headerText);
            log.info("\n>>>>>>>>>>>>>>> 第{}页footerText <<<<<<<<<<<<<<<\n{}", pageNo, footerText);
        });

    }


    /**
     * 提取body正文
     *
     * @throws Exception
     */
    @Test
    public void testExtractBodyContentByFile() throws Exception {
        int pageNo = 3;
        URL resource = ClassLoader.getSystemResource("./file/example.docx");
        File file = new File(resource.toURI());
        String content = contentExtractService.getBodyContentFormPageNo(file, pageNo);
        log.info("第{}页body内容:\n{}", pageNo, content);
    }

    /**
     * 提取页脚
     *
     * @throws Exception
     */
    @Test
    public void testExtractFooterContentByFile() throws Exception {
        int pageNo = 3;
        URL resource = ClassLoader.getSystemResource("./file/example.docx");
        File file = new File(resource.toURI());
        String content = contentExtractService.getFooterContentFormPageNo(file, pageNo);
        log.info("第{}页脚内容:\n{}", pageNo, content);
    }

    /**
     * 提取页眉
     *
     * @throws Exception
     */
    @Test
    public void testExtractHeaderContentByFile() throws Exception {
        int pageNo = 3;
        URL resource = ClassLoader.getSystemResource("./file/example.docx");
        File file = new File(resource.toURI());
        String content = contentExtractService.getHeaderContentFormPageNo(file, pageNo);
        log.info("第{}页眉内容:\n{}", pageNo, content);
    }
}