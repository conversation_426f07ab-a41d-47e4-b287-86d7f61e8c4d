package net.qiyuesuo.storage;


import net.qiyuesuo.common.crypt.MD5;
import org.apache.commons.compress.utils.IOUtils;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.CipherInputStream;
import javax.crypto.CipherOutputStream;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.ByteArrayInputStream;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;

import java.security.InvalidKeyException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Arrays;
import java.util.UUID;

/**
 * 对4.3.0之前版本的存储文件进行解密
 */
public class EncryptUtils {
    private static final String BUCKET = "qiyuesuo-test-hw";

	public static void main(String[] args) throws Exception {

        // 解密密钥错误但是没抛异常的文件
//		try (FileInputStream fis = new FileInputStream("/Users/<USER>/Downloads/rB4GCme4GG-AJCJeAAXjAHD-irQ051.pdf");
//             // 输出文件
//			 FileOutputStream fos = new FileOutputStream("/Users/<USER>/Downloads/rB4GCme4GG-AJCJeAAXjAHD-irQ051-1.pdf")) {
//			byte[] bytes = IOUtils.toByteArray(fis);
//            // 文件密钥
////			byte[] decrypt = decrypt(bytes, "20250221-c73ae705-e752-481c-acf6-06d999d3a30f");
//			byte[] decrypt = decrypt(bytes,"63e9f5ab9df145b1");
//
//			IOUtils.copy(new ByteArrayInputStream(decrypt), fos);
//		}

		// 解密密钥错误但是没抛异常的文件，（Invalid padding 也没抛）
		try (FileInputStream fis = new FileInputStream("/Users/<USER>/Downloads/rB4GDmfAKSCAc86xAAaQcOKPZkI639.pdf");
			 // 输出文件
			 FileOutputStream fos = new FileOutputStream("/Users/<USER>/Downloads/rB4GDmfAKSCAc86xAAaQcOKPZkI639-1.pdf")) {
			byte[] bytes = IOUtils.toByteArray(fis);
			// 文件密钥（不同的错误密钥，解密的效果不一样，有的抛异常：
			// javax.crypto.BadPaddingException: Given final block not properly padded. Such issues can arise if a bad key is used during decryption.
			// 有的不抛异常，但是最后一个字节是不合法的填充值，只有用这个密钥：20250227-faf38bee-518c-4611-aa36-51326c06aad7）
			byte[] decrypt = decrypt(bytes, "20250227-faf38bee-518c-4611-aa36-51326c06aad7");
//			byte[] decrypt = decrypt(bytes,"8a1cca3eb91f41ff");

			IOUtils.copy(new ByteArrayInputStream(decrypt), fos);
		}

		// 解密密钥错误但是抛了异常的文件
//		try (FileInputStream fis = new FileInputStream("/Users/<USER>/Downloads/rB4GDme9jMyALF8MAAao0OLylW8084.pdf");
//			 // 输出文件
//			 FileOutputStream fos = new FileOutputStream("/Users/<USER>/Downloads/rB4GDme9jMyALF8MAAao0OLylW8084-1.pdf")) {
//			byte[] bytes = IOUtils.toByteArray(fis);
//			// 文件密钥
////			byte[] decrypt = decrypt(bytes, "20250221-c73ae705-e752-481c-acf6-06d999d3a30f");
//			byte[] decrypt = decrypt(bytes,"63e9f5ab9df145b1");
//
//			IOUtils.copy(new ByteArrayInputStream(decrypt), fos);
//		}
    }

	public static String generateKey() {
		return UUID.randomUUID().toString().replaceAll("-", "").substring(0, 16);
	}

	public static InputStream encryptStream(InputStream in, String secretKey) throws Exception {
		Cipher cipher = Cipher.getInstance("AES");
		cipher.init(Cipher.ENCRYPT_MODE, new SecretKeySpec(getAesKey(secretKey), "AES"));
		return new CipherInputStream(in, cipher);
	}

	private static byte[] getAesKey(String secretKey) {
		byte[] bk = MD5.toMD5("PRIV@QYS" + secretKey).getBytes(StandardCharsets.UTF_8);
		return Arrays.copyOf(bk, bk.length / 2);
	}

	public static void encryStream(InputStream in, OutputStream out, String key) throws NoSuchPaddingException, NoSuchAlgorithmException, IOException, InvalidKeyException {
		Cipher cipher = Cipher.getInstance("AES");
		byte[] bk = MD5.toMD5("PRIV@QYS" + key).getBytes("UTF-8");
		bk = Arrays.copyOf(bk, bk.length / 2);
		cipher.init(Cipher.ENCRYPT_MODE, new SecretKeySpec(bk, "AES"));
		try (CipherOutputStream cipherOutputStream = new CipherOutputStream(out, cipher);) {
			byte[] bytes = new byte[1024];
			int nRead;
			while ((nRead = in.read(bytes)) != -1) {
				cipherOutputStream.write(bytes, 0, nRead);
				cipherOutputStream.flush();
			}
		}
	}

	public static void decryptStream(InputStream in, OutputStream out, String key) throws NoSuchPaddingException, NoSuchAlgorithmException, UnsupportedEncodingException {
		Cipher cipher = Cipher.getInstance("AES");
		String md5str = MD5.toMD5("PRIV@QYS" + key);
		byte[] bk = md5str.getBytes("UTF-8");
		try {
			byte[] bk16 = Arrays.copyOf(bk, bk.length / 2);
			cipher.init(Cipher.DECRYPT_MODE, new SecretKeySpec(bk16, "AES"));
			try (CipherOutputStream cipherOutputStream = new CipherOutputStream(out, cipher);) {
				byte[] bytes = new byte[1024];
				int nRead;
				while ((nRead = in.read(bytes)) != -1) {
					cipherOutputStream.write(bytes, 0, nRead);
					cipherOutputStream.flush();
				}
			}
		} catch (Exception e) {
			throw new RuntimeException("文件" + key + "解密失败", e);
		}
	}

	public static String hashStream(String path) {
		try (InputStream inputStream = new FileInputStream(path)) {
			MessageDigest messageDigest = MessageDigest.getInstance("SHA-256");
			byte[] buffer = new byte[8192];
			int length;
			while ((length = inputStream.read(buffer)) != -1) {
				messageDigest.update(buffer, 0, length);
			}
			return byte2Hex(messageDigest.digest());
		} catch (Exception e) {
			return null;
		}
	}

	public static String hashBytes(String path) {
		try (FileInputStream fileInputStream = new FileInputStream(path)) {
			MessageDigest messageDigest = MessageDigest.getInstance("SHA-256");
			messageDigest.update(IOUtils.toByteArray(fileInputStream));
			return byte2Hex(messageDigest.digest());
		} catch (Exception e) {
			return null;
		}
	}

	public static String byte2Hex(byte[] b) {
		if (b == null) {
			throw new IllegalArgumentException("Argument b ( byte array ) is null! ");
		} else {
			String hs = "";
			String stmp = "";

			for(int n = 0; n < b.length; ++n) {
				stmp = Integer.toHexString(b[n] & 255);
				if (stmp.length() == 1) {
					hs = hs + "0" + stmp;
				} else {
					hs = hs + stmp;
				}
			}

			return hs;
		}
	}

	public static long getEncryptSize(long size) {
		return ((size / 16) + 1) * 16;
	}

	private static byte[] decrypt(byte[] fileBytes, String fileKey) throws Exception {
		long length = fileBytes.length;
		System.out.println("加密文件长度是否为 16 的整数倍" + (length % 16 == 0)); // 若为 true，说明数据对齐

		Cipher cipher = Cipher.getInstance("AES");

		String md5str =  MD5.toMD5("PRIV@QYS" + fileKey);
		byte[] bk = md5str.getBytes("UTF-8");
		try {
			byte[] bk16 = Arrays.copyOf(bk, bk.length / 2);
			cipher.init(Cipher.DECRYPT_MODE, new SecretKeySpec(bk16, "AES"));
			byte[] fbyte = cipher.doFinal(fileBytes);
			//					// 在解密后手动校验填充合法性（即使 doFinal() 未抛出异常）：
//					// 检查最后一个字节是否为合法填充值
					int paddingValue = fbyte[fbyte.length - 1] & 0xFF;
					if (paddingValue < 1 || paddingValue > 16) {
						throw new BadPaddingException("Invalid padding");
					}
					for (int i = fbyte.length - paddingValue; i < fbyte.length; i++) {
						if (fbyte[i] != paddingValue) {
							throw new BadPaddingException("Invalid padding");
						}
					}

			return fbyte;
		} catch (Exception e) {
			e.printStackTrace();
			System.out.println("====================111");
			bk = md5str.toUpperCase().getBytes("UTF-8");
			try {
				byte[] bk16 = Arrays.copyOf(bk, bk.length / 2);
				cipher.init(Cipher.DECRYPT_MODE, new SecretKeySpec(bk16, "AES"));
				return cipher.doFinal(fileBytes);
			} catch (Exception e2) {
				e2.printStackTrace();
				System.out.println("====================222");
				byte[] fbyte = null;
				try {
					cipher.init(Cipher.DECRYPT_MODE, new SecretKeySpec(bk, "AES"));
					fbyte = cipher.doFinal(fileBytes);

//					// 在解密后手动校验填充合法性（即使 doFinal() 未抛出异常）：
//					// 检查最后一个字节是否为合法填充值
					int paddingValue = fbyte[fbyte.length - 1] & 0xFF;
					if (paddingValue < 1 || paddingValue > 16) {
						throw new BadPaddingException("Invalid padding");
					}


				} catch (Exception e3) {
					e3.printStackTrace();
					System.out.println("====================333");
				}

				return fbyte;
			}
		}
	}

    private static byte[] encrypt(byte[] fileBytes, String fileKey) throws Exception {
        Cipher cipher = Cipher.getInstance("AES");
        String md5str =  MD5.toMD5("PRIV@QYS" + fileKey);
        byte[] bk = md5str.getBytes("UTF-8");
        try {
            byte[] bk16 = Arrays.copyOf(bk, bk.length / 2);
            cipher.init(Cipher.DECRYPT_MODE, new SecretKeySpec(bk16, "AES"));
            return cipher.doFinal(fileBytes);
        } catch (Exception e) {
            bk = md5str.toUpperCase().getBytes("UTF-8");
            try {
                byte[] bk16 = Arrays.copyOf(bk, bk.length / 2);
                cipher.init(Cipher.DECRYPT_MODE, new SecretKeySpec(bk16, "AES"));
                return cipher.doFinal(fileBytes);
            } catch (Exception e2) {
                cipher.init(Cipher.DECRYPT_MODE, new SecretKeySpec(bk, "AES"));
                return cipher.doFinal(fileBytes);
            }
        }
    }
}
