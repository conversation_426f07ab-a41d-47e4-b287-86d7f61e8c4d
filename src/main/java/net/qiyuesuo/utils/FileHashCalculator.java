package net.qiyuesuo.utils;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

public class FileHashCalculator {
	public static String calculateFileHash(String filePath, String algorithm) {
		try {
			// 创建MessageDigest对象，指定哈希算法
			MessageDigest digest = MessageDigest.getInstance(algorithm);
			// 读取文件内容
			FileInputStream fis = new FileInputStream(new File(filePath));
			byte[] buffer = new byte[8192];
			int bytesRead;
			while ((bytesRead = fis.read(buffer)) != -1) {
				digest.update(buffer, 0, bytesRead);
			}
			fis.close();
			// 计算哈希值
			byte[] hashBytes = digest.digest();
			// 将字节数组转换为十六进制字符串
			StringBuilder hexString = new StringBuilder();
			for (byte hashByte : hashBytes) {
				String hex = Integer.toHexString(0xff & hashByte);
				if (hex.length() == 1) {
					hexString.append('0');
				}
				hexString.append(hex);
			}
			return hexString.toString();
		} catch (NoSuchAlgorithmException | IOException e) {
			e.printStackTrace();
			return null;
		}
	}

	public static void main(String[] args) {
		String filePath = "/Users/<USER>/Downloads/图纸_K001T-C023_基建进度计划图表_0_back.pdf";
		String algorithm = "SHA-256"; // 可以根据需要选择其他哈希算法，如MD5、SHA-1等
		String fileHash = calculateFileHash(filePath, algorithm);
		if (fileHash != null) {
			System.out.println("文件的 " + algorithm + " 哈希值是: " + fileHash);
		} else {
			System.out.println("计算哈希值时发生错误。");
		}
	}
}