package net.qiyuesuo.deepseek;

import com.volcengine.ark.runtime.model.completion.chat.ChatCompletionRequest;
import com.volcengine.ark.runtime.model.completion.chat.ChatMessage;
import com.volcengine.ark.runtime.model.completion.chat.ChatMessageRole;
import com.volcengine.ark.runtime.service.ArkService;
import okhttp3.ConnectionPool;
import okhttp3.Dispatcher;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

public class ChatCompletionsExample {
	static String apiKey = "58a0781d-9a83-4ca0-807f-0c4f30456086";//System.getenv("58a0781d-9a83-4ca0-807f-0c4f30456086");
	static ConnectionPool connectionPool = new ConnectionPool(5, 1, TimeUnit.SECONDS);
	static Dispatcher dispatcher = new Dispatcher();
	static ArkService service = ArkService.builder().dispatcher(dispatcher).connectionPool(connectionPool).baseUrl("https://ark.cn-beijing.volces.com/api/v3").apiKey(apiKey).build();

	public static void main(String[] args) {
		System.out.println("\n----- standard request -----");
		final List<ChatMessage> messages = new ArrayList<>();
		final ChatMessage systemMessage = ChatMessage.builder().role(ChatMessageRole.SYSTEM).content("你是豆包，是由字节跳动开发的 AI 人工智能助手").build();
		final ChatMessage userMessage = ChatMessage.builder().role(ChatMessageRole.USER).content("常见的十字花科植物有哪些？").build();
		messages.add(systemMessage);
		messages.add(userMessage);

		ChatCompletionRequest chatCompletionRequest = ChatCompletionRequest.builder()
				.model("ep-20250211141601-bsrsn")
				.messages(messages)
				.build();

		service.createChatCompletion(chatCompletionRequest).getChoices().forEach(choice -> System.out.println(choice.getMessage().getContent()));

		System.out.println("\n----- streaming request -----");
		final List<ChatMessage> streamMessages = new ArrayList<>();
		final ChatMessage streamSystemMessage = ChatMessage.builder().role(ChatMessageRole.SYSTEM).content("你是豆包，是由字节跳动开发的 AI 人工智能助手").build();
		final ChatMessage streamUserMessage = ChatMessage.builder().role(ChatMessageRole.USER).content("常见的十字花科植物有哪些？").build();
		streamMessages.add(streamSystemMessage);
		streamMessages.add(streamUserMessage);

		ChatCompletionRequest streamChatCompletionRequest = ChatCompletionRequest.builder()
				.model("ep-20250211141601-bsrsn")
				.messages(streamMessages)
				.build();

		service.streamChatCompletion(streamChatCompletionRequest)
				.doOnError(Throwable::printStackTrace)
				.blockingForEach(
						choice -> {
							if (choice.getChoices().size() > 0) {
								System.out.print(choice.getChoices().get(0).getMessage().getContent());
							}
						}
				);

		// shutdown service after all requests is finished
		service.shutdownExecutor();
	}

}