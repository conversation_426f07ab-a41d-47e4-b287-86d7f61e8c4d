package net.qiyuesuo.poi;

import org.apache.poi.xwpf.usermodel.*;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTSectPr;

import java.io.*;
import java.util.ArrayList;
import java.util.List;

/**
 * 只支持 docx文件
 */
public class WordPageExtractor {

	public static void main(String[] args) {
		String filePath = "/Users/<USER>/Downloads/台州银行行政电子印章系统设计说明书.doc";

		int targetPage = 1; // 要提取的页码，从 1 开始
		try {
			List<String> pageContent = extractPageContent(filePath, targetPage);
			for (String line : pageContent) {
				System.out.println(line);
			}
		} catch (IOException e) {
			e.printStackTrace();
		}
	}
	public static List<String> extractPageContent(String filePath, int targetPage) throws IOException {
		List<String> content = new ArrayList<>();
		try (FileInputStream fis = new FileInputStream(filePath);
			 XWPFDocument document = new XWPFDocument(fis)) {

			int currentPage = 1;
			boolean isTargetPage = false;

			// 遍历文档中的所有节点（段落和表格）
			for (IBodyElement element : document.getBodyElements()) {
				if (element instanceof XWPFParagraph) {
					XWPFParagraph paragraph = (XWPFParagraph) element;
					// 检查段落中是否有分页符
					for (XWPFRun run : paragraph.getRuns()) {
						if (run.getCTR().getBrList() != null) {
							for (org.openxmlformats.schemas.wordprocessingml.x2006.main.CTBr br : run.getCTR().getBrList()) {
								if (br.getType() != null && br.getType().equals(org.openxmlformats.schemas.wordprocessingml.x2006.main.STBrType.PAGE)) {
									currentPage++;
								}
							}
						}
					}

					if (currentPage == targetPage) {
						isTargetPage = true;
					} else if (currentPage > targetPage) {
						break;
					}

					if (isTargetPage) {
						content.add(paragraph.getText());
					}
				} else if (element instanceof XWPFTable) {
					XWPFTable table = (XWPFTable) element;
					// 检查表格第一个单元格所在段落是否有分页符（简单判断）
					XWPFParagraph firstCellParagraph = table.getRows().get(0).getCell(0).getParagraphs().get(0);
					for (XWPFRun run : firstCellParagraph.getRuns()) {
						if (run.getCTR().getBrList() != null) {
							for (org.openxmlformats.schemas.wordprocessingml.x2006.main.CTBr br : run.getCTR().getBrList()) {
								if (br.getType() != null && br.getType().equals(org.openxmlformats.schemas.wordprocessingml.x2006.main.STBrType.PAGE)) {
									currentPage++;
								}
							}
						}
					}

					if (currentPage == targetPage) {
						isTargetPage = true;
					} else if (currentPage > targetPage) {
						break;
					}

					if (isTargetPage) {
						// 处理表格数据
						for (XWPFTableRow row : table.getRows()) {
							StringBuilder rowData = new StringBuilder();
							for (XWPFTableCell cell : row.getTableCells()) {
								rowData.append(cell.getText()).append("\t");
							}
							content.add(rowData.toString());
						}
					}
				}
			}
		}
		return content;
	}
}
