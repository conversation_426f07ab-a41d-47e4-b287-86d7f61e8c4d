package net.qiyuesuo.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * 此文件测试为什么浏览器中访问controller中接口，访问不到
 * 原因：@ConditionalOnProperty 此属性导致，需要在配置文件中添加 qiyuesuo.upgrade.manual=true 才能访问到
 */
@Controller
@RequestMapping("/upgrade")
@ConditionalOnProperty(value = "qiyuesuo.upgrade.manual", havingValue="true", matchIfMissing=false)
public class UpgradeController implements ApplicationContextAware {
	protected final Logger logger = LoggerFactory.getLogger(getClass());
	private ApplicationContext applicationContext;

	@RequestMapping(path = "/query", method = RequestMethod.GET)
	@ResponseBody
	public String versions(String param) {
		logger.info(param);
		return param;
	}

	@Override
	public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
		this.applicationContext = applicationContext;
	}
}
