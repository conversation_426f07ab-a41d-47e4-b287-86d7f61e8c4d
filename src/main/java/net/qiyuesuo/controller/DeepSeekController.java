package net.qiyuesuo.controller;

import io.github.pigmesh.ai.deepseek.config.DeepSeekProperties;
import io.github.pigmesh.ai.deepseek.core.DeepSeekClient;
import io.github.pigmesh.ai.deepseek.core.chat.ChatCompletionModel;
import io.github.pigmesh.ai.deepseek.core.chat.ChatCompletionRequest;
import io.github.pigmesh.ai.deepseek.core.chat.ChatCompletionResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import reactor.core.publisher.Flux;

@Controller
@RequestMapping("/ds")
public class DeepSeekController implements ApplicationContextAware {
	protected final Logger logger = LoggerFactory.getLogger(getClass());
	private ApplicationContext applicationContext;

	@Autowired
	private DeepSeekClient deepSeekClient;

	@Override
	public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
		this.applicationContext = applicationContext;
	}

	@GetMapping(value = "/chat", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
	public Flux<ChatCompletionResponse> chat(String prompt) {
		System.out.println("提问：" + prompt);
		return deepSeekClient.chatFluxCompletion(prompt);
	}

	@GetMapping(value = "/chat/advanced", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
	public Flux<ChatCompletionResponse> chatAdvanced(String prompt) {
		ChatCompletionRequest request = ChatCompletionRequest.builder()
				// 模型选择，支持 DEEPSEEK_CHAT、DEEPSEEK_REASONER 等
				.model(ChatCompletionModel.DEEPSEEK_REASONER)
				// 添加用户消息
				.addUserMessage(prompt)
				// 添加助手消息，用于多轮对话
				.addAssistantMessage("上轮结果")
				// 添加系统消息，用于设置角色和行为
				.addSystemMessage("你是一个专业的助手")
				// 设置最大生成 token 数，默认 2048
				.maxTokens(1000)
				// 设置响应格式，支持 JSON 结构化输出
//				.responseFormat(...) // 可选
//				// function calling
//				.tools(...) // 可选
				.build();

		return deepSeekClient.chatFluxCompletion(request);
	}
}
