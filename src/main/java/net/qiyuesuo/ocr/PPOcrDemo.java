//package net.qiyuesuo.ocr;
//
//import com.benjaminwan.ocrlibrary.OcrResult;
//import io.github.mymonstercat.Model;
//import io.github.mymonstercat.ocr.InferenceEngine;
//import io.github.mymonstercat.ocr.config.ParamConfig;
//import org.apache.pdfbox.filter.Predictor;
//import org.opencv.core.Core;
//import org.opencv.core.CvType;
//import org.opencv.core.Mat;
//import org.opencv.imgcodecs.Imgcodecs;
//import org.opencv.imgproc.Imgproc;
//
//import java.io.BufferedReader;
//import java.io.InputStreamReader;
//import java.util.ArrayList;
//import java.util.List;
//
//import org.opencv.core.*;
//		import org.opencv.imgcodecs.Imgcodecs;
//import org.paddlepaddle.paddle.inference.Predictor;
//import org.paddlepaddle.paddle.inference.PaddleConfig;
//import org.paddlepaddle.paddle.inference.Tensor;
//
//import java.io.BufferedReader;
//import java.io.File;
//import java.io.IOException;
//import java.io.InputStreamReader;
//import java.nio.FloatBuffer;
//import java.util.ArrayList;
//import java.util.List;
//
//public class PPOcrDemo {
//	static {
//		// 加载 OpenCV 库
//		System.loadLibrary(Core.NATIVE_LIBRARY_NAME);
//	}
//
//	private Predictor detPredictor;
//	private Predictor recPredictor;
//	private List<String> dictionary;
//
//	public PPOcrDemo() {
//		// 加载检测模型
//		PaddleConfig detConfig = new PaddleConfig();
//		String detModelPath = ImageOCR.class.getClassLoader().getResource("models/ch_PP-OCRv3_det_infer").getPath();
//		detConfig.setModelDir(detModelPath);
//		detPredictor = new Predictor(detConfig);
//
//		// 加载识别模型
//		PaddleConfig recConfig = new PaddleConfig();
//		String recModelPath = ImageOCR.class.getClassLoader().getResource("models/ch_PP-OCRv3_rec_infer").getPath();
//		recConfig.setModelDir(recModelPath);
//		recPredictor = new Predictor(recConfig);
//
//		// 加载字典文件
//		dictionary = loadDictionary();
//	}
//
//	private List<String> loadDictionary() {
//		List<String> dictionary = new ArrayList<>();
//		try (BufferedReader reader = new BufferedReader(new InputStreamReader(
//				ImageOCR.class.getClassLoader().getResourceAsStream("models/ppocr_keys_v1.txt")))) {
//			String line;
//			while ((line = reader.readLine()) != null) {
//				dictionary.add(line);
//			}
//		} catch (IOException e) {
//			e.printStackTrace();
//		}
//		return dictionary;
//	}
//
//	public String recognizeImage(File imageFile) {
//		// 读取图像
//		Mat image = Imgcodecs.imread(imageFile.getAbsolutePath());
//		if (image.empty()) {
//			System.err.println("无法读取图像: " + imageFile.getAbsolutePath());
//			return "";
//		}
//
//		// 图像预处理（这里简化，实际应用中需要更复杂的处理）
//		Mat resizedImage = new Mat();
//		Size size = new Size(640, 640);
//		Imgproc.resize(image, resizedImage, size);
//		Mat normalizedImage = new Mat();
//		resizedImage.convertTo(normalizedImage, CvType.CV_32F, 1.0 / 255.0);
//
//		// 进行文本检测
//		Tensor detInput = detPredictor.getInputTensor(0);
//		float[] inputData = matToFloatArray(normalizedImage);
//		detInput.copyFromCpu(inputData);
//		detPredictor.run();
//		Tensor detOutput = detPredictor.getOutputTensor(0);
//		float[] detResult = detOutput.copyToCpuFloatData();
//
//		// 这里需要根据检测结果裁剪出文本区域，并进行预处理
//		// 简化示例，假设已经得到裁剪后的文本区域图像 Mat croppedImage
//		Mat croppedImage = resizedImage;
//
//		// 进行文本识别
//		Tensor recInput = recPredictor.getInputTensor(0);
//		float[] recInputData = matToFloatArray(croppedImage);
//		recInput.copyFromCpu(recInputData);
//		recPredictor.run();
//		Tensor recOutput = recPredictor.getOutputTensor(0);
//		float[] recResult = recOutput.copyToCpuFloatData();
//
//		// 后处理，将识别结果转换为文本
//		String text = resultToText(recResult);
//		return text;
//	}
//
//	private float[] matToFloatArray(Mat mat) {
//		int rows = mat.rows();
//		int cols = mat.cols();
//		int channels = mat.channels();
//		float[] data = new float[rows * cols * channels];
//		mat.get(0, 0, data);
//		return data;
//	}
//
//	private String resultToText(float[] result) {
//		StringBuilder text = new StringBuilder();
//		for (float index : result) {
//			int idx = (int) index;
//			if (idx < dictionary.size()) {
//				text.append(dictionary.get(idx));
//			}
//		}
//		return text.toString();
//	}
//
//	public void recognizeImagesInDirectory(String directoryPath) {
//		File directory = new File(directoryPath);
//		if (!directory.exists() || !directory.isDirectory()) {
//			System.err.println("指定的目录不存在: " + directoryPath);
//			return;
//		}
//
//		File[] imageFiles = directory.listFiles((dir, name) ->
//				name.endsWith(".jpg") || name.endsWith(".jpeg") || name.endsWith(".png"));
//		if (imageFiles == null) {
//			System.err.println("未找到图像文件");
//			return;
//		}
//
//		for (File imageFile : imageFiles) {
//			String text = recognizeImage(imageFile);
//			System.out.println("图像: " + imageFile.getName() + " 识别结果: " + text);
//		}
//	}
//
//	public static void main(String[] args) {
//		ImageOCR ocr = new ImageOCR();
//		// 指定要识别的目录路径
//		String directoryPath = "path/to/your/image/directory";
//		ocr.recognizeImagesInDirectory(directoryPath);
//	}
//}