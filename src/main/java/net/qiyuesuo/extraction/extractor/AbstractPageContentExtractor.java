package net.qiyuesuo.extraction.extractor;

import net.qiyuesuo.extraction.config.ExtractOption;
import net.qiyuesuo.extraction.config.ExtractResult;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Collections;

public abstract class AbstractPageContentExtractor implements PageContentExtractor {

    @Override
    public ExtractResult extractContent(ExtractOption option) throws Exception {
        this.validate(option);

        return this.doExtract(option);
    }

    /**
     * 校验
     *
     * @param option
     */
    protected void validate(ExtractOption option) {
        if (option.getBytes() == null || option.getBytes().length == 0) {
            throw new IllegalArgumentException("Input is null");
        }
        if (CollectionUtils.isEmpty(option.getPageNumbers())) {
            throw new IllegalArgumentException("PageNumbers is empty");
        }
        if (option.getPageNumbers().stream().anyMatch(i -> i <= 0)) {
            throw new IllegalArgumentException("Illegal number of page");
        }
    }

    /**
     * 提取
     *
     * @param option
     * @return
     */
    protected abstract ExtractResult doExtract(ExtractOption option) throws Exception;


    @Override
    public ExtractResult extractBodyContent(byte[] bytes, int pageNo) throws Exception {
        ExtractOption option = ExtractOption.builder()
                .bytes(bytes)
                .pageNumbers(Collections.singletonList(pageNo))
                .body(true)
                .build();
        return this.extractContent(option);
    }

    @Override
    public ExtractResult extractHeaderContent(byte[] bytes, int pageNo) throws Exception {
        ExtractOption option = ExtractOption.builder()
                .bytes(bytes)
                .pageNumbers(Collections.singletonList(pageNo))
                .header(true)
                .build();
        return this.extractContent(option);
    }

    @Override
    public ExtractResult extractFooterContent(byte[] bytes, int pageNo) throws Exception {
        ExtractOption option = ExtractOption.builder()
                .bytes(bytes)
                .pageNumbers(Collections.singletonList(pageNo))
                .footer(true)
                .build();
        return this.extractContent(option);
    }

}
