package net.qiyuesuo.extraction.extractor.word;

import com.aspose.words.*;
//import com.itextpdf.text.pdf.PdfReader;
//import com.itextpdf.text.pdf.parser.PdfReaderContentParser;
//import com.itextpdf.text.pdf.parser.SimpleTextExtractionStrategy;
//import com.itextpdf.text.pdf.parser.TextExtractionStrategy;
import lombok.extern.slf4j.Slf4j;
import net.qiyuesuo.common.word.WordService;
import net.qiyuesuo.common.word.confiden.Confidential;
import net.qiyuesuo.extraction.config.ExtractOption;
import net.qiyuesuo.extraction.config.ExtractResult;
import net.qiyuesuo.extraction.extractor.AbstractPageContentExtractor;
import org.apache.commons.lang3.BooleanUtils;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.regex.Pattern;

/**
 * 基于Itext的word页面内容提取器
 */
@Slf4j
public class ItextWordContentExtractor extends AbstractPageContentExtractor {

    ItextWordContentExtractor() {
        Confidential confidential = new Confidential();
        License license = new License();
        try {
            String newLicence = confidential.decrypt(WordService.licenseString, WordService.salt);
            license.setLicense(new ByteArrayInputStream(newLicence.getBytes()));
        } catch (Exception e) {
        }
    }


    @Override
    protected ExtractResult doExtract(ExtractOption option) throws Exception {


        boolean needBody = BooleanUtils.isTrue(option.getBody());
        boolean needHeader = BooleanUtils.isTrue(option.getHeader());
        boolean needFooter = BooleanUtils.isTrue(option.getFooter());
        ExtractResult result = ExtractResult.builder()
                .bodyTextMap(needBody ? new HashMap<>() : null)
                .headerTextMap(needHeader ? new HashMap<>() : null)
                .footerTextMap(needFooter ? new HashMap<>() : null)
                .build();

        if (needBody) {
            // 处理页眉和页脚，转成pdf
            processAndConvert(option);
        }

        List<Integer> pageNumbers = option.getPageNumbers();

        for (Integer pageNumber : pageNumbers) {
            if (needHeader) {
                throw new UnsupportedOperationException("Getting header is not supported");
            }
            if (needFooter) {
                throw new UnsupportedOperationException("Getting footer is not supported");
            }
            if (needBody) {
//                //提取指定页正文
//                PdfReader reader = new PdfReader(option.getBytes());
//                PdfReaderContentParser parser = new PdfReaderContentParser(reader);
//                TextExtractionStrategy strategy = parser.processContent(pageNumber, new SimpleTextExtractionStrategy());
//                String bodyText = strategy.getResultantText();
//
//                result.addBodyText(pageNumber, compressText(bodyText));
            }
        }
        return result;
    }

    /**
     * // 处理页眉和页脚，然后转成pdf
     *
     * @param option
     * @throws Exception
     */
    private void processAndConvert(ExtractOption option) throws Exception {
        try (
                ByteArrayInputStream inputStream = new ByteArrayInputStream(option.getBytes());
                ByteArrayOutputStream outputStream = new ByteArrayOutputStream()
        ) {

            processWord(inputStream, outputStream, option);

            byte[] wordBytes = outputStream.toByteArray();

            saveWord(wordBytes);

            byte[] pdfBytes = new WordService().convertToPdf2(wordBytes, null);

            savePdf(pdfBytes);

            option.setBytes(pdfBytes);


        }
    }

    /**
     * 处理word文件
     *
     * @param inputStream
     * @param outputStream
     * @param option
     * @throws Exception
     */
    private void processWord(ByteArrayInputStream inputStream, ByteArrayOutputStream outputStream, ExtractOption option) throws Exception {
        Document document = new Document(inputStream);

        // 文本替换配置
        FindReplaceOptions replaceOptions = option.getReplaceOptions();
        if (replaceOptions == null) {
            replaceOptions = new FindReplaceOptions();
        }
        replaceOptions.setFindWholeWordsOnly(false); // 全词匹配
        replaceOptions.setMatchCase(false); // 大小写匹配

        // 去除页眉和页脚
        for (Section section : document.getSections()) {
            HeaderFooterCollection headersFooters = section.getHeadersFooters();
            for (int type : HeaderFooterType.getValues()) {
                HeaderFooter headerFooter = headersFooters.get(type);
                if (headerFooter == null) {
                    continue;
                }
                Range range = headerFooter.getRange();
                if (range == null) {
                    continue;
                }
                replaceCN(replaceOptions, range);
                replaceNotCN(replaceOptions, range);
            }
        }
        document.save(outputStream, SaveFormat.DOCX);
    }


    private static final String CN_PLACEHOLDER = "\u200B"; // 零宽空格

    private static int replaceCN(FindReplaceOptions replaceOptions, Range range) throws Exception {
        return range.replace(Pattern.compile("(?s)[\\u4e00-\\u9fa5]"), CN_PLACEHOLDER, replaceOptions);
    }

    private static final String DEFAULT_PLACEHOLDER = " ";

    private static int replaceNotCN(FindReplaceOptions replaceOptions, Range range) throws Exception {
        return range.replace(Pattern.compile("(?s)[^\\u4e00-\\u9fa5\\s]"), DEFAULT_PLACEHOLDER, replaceOptions);
    }

    /**
     * 文本处理
     *
     * @param text
     * @return
     */
    private String compressText(String text) {
        if (text == null) {
            return null;
        }
        return text.trim()
                .replace("\r", "")
                .replace("\n", "") // 压缩文本
                .replace(CN_PLACEHOLDER, "") // 去除零宽空格
                .replaceAll("[\\x00-\\x1F\\x7F-\\xFF]", ""); // 去除不常用的ASCII字符

    }

    // save to local
    private void saveWord(byte[] wordBytes) throws IOException {
        URL resource = ClassLoader.getSystemResource(".");
        String path = resource.getPath() + "【Itext】pageTest-" + new Date().getTime() + ".docx";
        log.info("\nthe test file is stored at:{}", path);
        Files.write(Paths.get(path), wordBytes);
    }


    // save to local
    private void savePdf(byte[] bytes) throws Exception {
        URL resource = ClassLoader.getSystemResource(".");
        String path = resource.getPath() + "【Itext】pageTest-" + new Date().getTime() + ".pdf";
        log.info("\nthe test file is stored at:{}", path);
        Files.write(Paths.get(path), bytes);
    }


}


