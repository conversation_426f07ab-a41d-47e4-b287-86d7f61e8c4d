package net.qiyuesuo.extraction.extractor.word;

import com.aspose.words.*;
import lombok.extern.slf4j.Slf4j;
import net.qiyuesuo.common.word.WordService;
import net.qiyuesuo.common.word.confiden.Confidential;
import net.qiyuesuo.extraction.config.ExtractOption;
import net.qiyuesuo.extraction.config.ExtractResult;
import net.qiyuesuo.extraction.extractor.AbstractPageContentExtractor;
import org.apache.commons.lang3.BooleanUtils;

import java.io.ByteArrayInputStream;
import java.net.URL;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * 基于Aspose的word页面内容提取器
 */
@Slf4j
public class AsposeWordContentExtractor extends AbstractPageContentExtractor {

    AsposeWordContentExtractor() {
        Confidential confidential = new Confidential();
        License license = new License();
        try {
            String newLicence = confidential.decrypt(WordService.licenseString, WordService.salt);
            license.setLicense(new ByteArrayInputStream(newLicence.getBytes()));
        } catch (Exception e) {

        }
    }

    @Override
    protected ExtractResult doExtract(ExtractOption option) throws Exception {
        byte[] bytes = option.getBytes();

        Document document;
        try (ByteArrayInputStream inputStream = new ByteArrayInputStream(bytes)) {
            document = new Document(inputStream);
        }

        boolean needBody = BooleanUtils.isTrue(option.getBody());
        boolean needHeader = BooleanUtils.isTrue(option.getHeader());
        boolean needFooter = BooleanUtils.isTrue(option.getFooter());

        ExtractResult result = ExtractResult.builder()
                .bodyTextMap(needBody ? new HashMap<>() : null)
                .headerTextMap(needHeader ? new HashMap<>() : null)
                .footerTextMap(needFooter ? new HashMap<>() : null)
                .build();

        List<Integer> pageNumbers = option.getPageNumbers();
        for (Integer pageNo : pageNumbers) {
            Document doc = document.extractPages(pageNo - 1, 1);

            Section section = doc.getFirstSection();

            if (needBody) {
                Body body = section.getBody();
                String text = compressText(body.toString(SaveFormat.TEXT));
                result.addBodyText(pageNo, text);
            }
            if (needHeader) {
                HeaderFooterCollection headersFooters = doc.getFirstSection().getHeadersFooters();
                HeaderFooter header = headersFooters.getByHeaderFooterType(HeaderFooterType.HEADER_PRIMARY);
                String headerText = null;
                if (header != null){
                    headerText = compressText(header.toString(SaveFormat.TEXT));
                }
                result.addHeaderText(pageNo, headerText);

            }
            if (needFooter) {
                HeaderFooterCollection headersFooters = doc.getFirstSection().getHeadersFooters();
                HeaderFooter footer = headersFooters.getByHeaderFooterType(HeaderFooterType.FOOTER_PRIMARY);
                String footerText =  null;
                if (footer != null){
                    footerText = compressText(footer.toString(SaveFormat.TEXT));
                }
                result.addFooterText(pageNo, footerText);
            }

            // FIXME: save to local
            localTest(pageNo, doc);
        }

        return result;
    }

    /**
     * 文本处理
     *
     * @param text
     * @return
     */
    private String compressText(String text) {
        if (text == null) {
            return null;
        }
        return text
                .replace("\r", "")
                .replace("\n", "") // 压缩文本
                .replace("\f", "") // 去除分页符
                .replaceAll("[\\x00-\\x1F\\x7F-\\xFF]", ""); // 去除不常用的ASCII字符

    }

    // save to local
    private void localTest(Integer pageNo, Document doc) throws Exception {
        URL resource = ClassLoader.getSystemResource(".");
        String path = resource.getPath() + "【Aspose】pageNo" + pageNo + "-" + new Date().getTime() + ".docx";
        log.info("\nthe test file is stored at:{}\n",path);
        doc.save(path, SaveFormat.DOCX);
        doc.cleanup();
    }

}
