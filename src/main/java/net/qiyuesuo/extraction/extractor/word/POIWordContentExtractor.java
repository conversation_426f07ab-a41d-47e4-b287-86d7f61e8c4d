package net.qiyuesuo.extraction.extractor.word;

import lombok.extern.slf4j.Slf4j;
import net.qiyuesuo.extraction.config.ExtractOption;
import net.qiyuesuo.extraction.config.ExtractResult;
import net.qiyuesuo.extraction.extractor.AbstractPageContentExtractor;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.poi.xwpf.extractor.XWPFWordExtractor;
import org.apache.poi.xwpf.model.XWPFHeaderFooterPolicy;
import org.apache.poi.xwpf.usermodel.*;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTBr;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTEmpty;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTR;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.STBrType;

import java.io.ByteArrayInputStream;
import java.io.OutputStream;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * 基于POI的word页面内容提取器
 */
@Slf4j
public class POIWordContentExtractor extends AbstractPageContentExtractor {

    POIWordContentExtractor() {
    }


    @Override
    protected ExtractResult doExtract(ExtractOption option) throws Exception {
        byte[] bytes = option.getBytes();

        XWPFDocument document;
        try (ByteArrayInputStream inputStream = new ByteArrayInputStream(bytes)) {
            document = new XWPFDocument(inputStream);
        }

        boolean needBody = BooleanUtils.isTrue(option.getBody());
        boolean needHeader = BooleanUtils.isTrue(option.getHeader());
        boolean needFooter = BooleanUtils.isTrue(option.getFooter());

        ExtractResult result = ExtractResult.builder()
                .bodyTextMap(needBody ? new HashMap<>() : null)
                .headerTextMap(needHeader ? new HashMap<>() : null)
                .footerTextMap(needFooter ? new HashMap<>() : null)
                .build();

        List<Integer> pageNumbers = option.getPageNumbers();

        // FIXME: 待处理
        {
            // FIXME:  分页检测
            List<XWPFParagraph> paragraphs = document.getParagraphs();
            for (XWPFParagraph paragraph : paragraphs) {
                if (paragraph.isPageBreak()) {
                    log.info("PageBreak detected !");
                    continue;
                }
                for (XWPFRun run : paragraph.getRuns()) {
                    // 解析<w:r>标签
                    CTR ctr = run.getCTR();
                    List<CTBr> brList = ctr.getBrList();
                    if (brList != null && !brList.isEmpty()) {
                        // 解析br标签
                        for (CTBr br : brList) {
                            if (br.getType() == STBrType.PAGE) {
                                log.info("br detected !");
                            }
                        }
                    } else {
                        // 解析lastRenderedPageBreak标签
                        List<CTEmpty> lastRenderedPageBreakList = ctr.getLastRenderedPageBreakList();
                        if (lastRenderedPageBreakList != null) {
                            for (CTEmpty lastRenderedPageBreak : lastRenderedPageBreakList) {
                                //page break detected
                                log.info("lastRenderedPageBreak detected !");
                            }
                        }
                    }
                }
            }
            // FIXME: 文本提取
            XWPFWordExtractor wordExtractor = new XWPFWordExtractor(document);
            String text = wordExtractor.getText();
//            log.info("text：{}", compressText(text));
        }

        for (Integer pageNumber : pageNumbers) {
            if (needBody) {
                throw new UnsupportedOperationException("Getting body is not supported");
            }
            XWPFHeaderFooterPolicy headerFooterPolicy = null;
            if (needHeader || needFooter) {
                headerFooterPolicy = new XWPFHeaderFooterPolicy(document);
            }
            if (needHeader) {
                XWPFHeader header = pageNumber == 1 ? headerFooterPolicy.getFirstPageHeader() : headerFooterPolicy.getDefaultHeader();
                String headerText = header == null ? null : header.getText();
                result.addHeaderText(pageNumber, compressText(headerText));
            }
            if (needFooter) {
                XWPFFooter footer = pageNumber == 1 ? headerFooterPolicy.getFirstPageFooter() : headerFooterPolicy.getDefaultFooter();
                String footerText = footer == null ? null : footer.getText();
                result.addFooterText(pageNumber, compressText(footerText));
            }

            // FIXME: save to local
            localTest(pageNumber, document);
        }

        return result;
    }

    /**
     * 文本处理
     *
     * @param text
     * @return
     */
    private String compressText(String text) {
        if (text == null) {
            return null;
        }
        return text
                .replace("\r", "")
                .replace("\n", "") // 压缩文本
                .replaceAll("[\\x00-\\x1F\\x7F-\\xFF]", ""); // 去除不常用的ASCII字符

    }

    // save to local
    private void localTest(Integer pageNo, XWPFDocument document) throws Exception {
        URL resource = ClassLoader.getSystemResource(".");
        String path = resource.getPath() + "【POI】pageNo" + pageNo + "-" + new Date().getTime() + ".docx";
        log.info("\nthe test file is stored at:{}\n",path);
        try (OutputStream outputStream = Files.newOutputStream(Paths.get(path))) {
            document.write(outputStream);
        }
    }


}


