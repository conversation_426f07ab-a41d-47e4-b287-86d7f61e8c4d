package net.qiyuesuo.extraction.extractor.word;

import net.qiyuesuo.extraction.config.enums.ExtractConfigEnum;
import net.qiyuesuo.extraction.extractor.AbstractSingletonInstancePageExtractorFactory;
import net.qiyuesuo.extraction.extractor.PageContentExtractor;

public class ItextWordContentExtractorFactory extends AbstractSingletonInstancePageExtractorFactory {

    @Override
    protected PageContentExtractor init() {
        return new ItextWordContentExtractor();
    }

    @Override
    public ExtractConfigEnum getType() {
        return ExtractConfigEnum.ITEXT;
    }
}
