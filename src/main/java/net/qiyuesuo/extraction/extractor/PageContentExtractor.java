package net.qiyuesuo.extraction.extractor;

import net.qiyuesuo.extraction.config.ExtractResult;

/**
 * 分页内容提取器
 */
public interface PageContentExtractor extends ContentExtractor {

    /**
     * 提取指定页面内容
     *
     * @param pageNo      指定页
     * @return
     * @throws Exception
     */
    ExtractResult extractBodyContent(byte[] bytes, int pageNo) throws Exception;

    /**
     * 提取指定页眉内容
     *
     * @param pageNo      指定页
     * @return
     * @throws Exception
     */
    ExtractResult extractHeaderContent(byte[] bytes, int pageNo) throws Exception;


    /**
     * 提取指定页脚内容
     *
     * @param pageNo      指定页
     * @return
     * @throws Exception
     */
    ExtractResult extractFooterContent(byte[] bytes, int pageNo) throws Exception;


}
