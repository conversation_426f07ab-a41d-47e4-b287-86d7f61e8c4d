package net.qiyuesuo.extraction.extractor;

import net.qiyuesuo.extraction.config.enums.ExtractConfigEnum;


public abstract class AbstractSingletonInstancePageExtractorFactory implements PageContentExtractorFactory {

    protected volatile PageContentExtractor instance;

    @Override
    public PageContentExtractor getInstance() {
        if (this.instance == null) {
            ExtractConfigEnum extractConfigEnum = this.getType();
            synchronized (extractConfigEnum.getType()) {
                if (this.instance == null) {
                    this.instance = init();
                }
            }
        }
        return this.instance;
    }

    protected abstract PageContentExtractor init();

    @Override
    public boolean isSupport(ExtractConfigEnum extractConfigEnum) {
        return this.getType().equals(extractConfigEnum);
    }

}
