package net.qiyuesuo.extraction.utils;

import com.google.common.collect.ImmutableList;
import lombok.extern.slf4j.Slf4j;
import net.qiyuesuo.common.word.WordService;
import net.qiyuesuo.extraction.config.enums.ExtractConfigEnum;
import net.qiyuesuo.extraction.config.properties.ExtractorProperties;
import net.qiyuesuo.extraction.extractor.PageContentExtractor;
import net.qiyuesuo.extraction.extractor.PageContentExtractorFactory;
import net.qiyuesuo.extraction.extractor.word.AsposeWordContentExtractorFactory;
import net.qiyuesuo.extraction.extractor.word.ItextWordContentExtractorFactory;
import net.qiyuesuo.extraction.extractor.word.POIWordContentExtractorFactory;

import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.List;

@Slf4j
public class ExtractorUtil {

    public static final List<ExtractConfigEnum> WORD_TYPE_ENUMS;
    public static final List<PageContentExtractorFactory> EXTRACTOR_FACTORIES;

    static {
        WORD_TYPE_ENUMS = Arrays.asList(ExtractConfigEnum.POI, ExtractConfigEnum.ASPOSE);
        EXTRACTOR_FACTORIES = ImmutableList.of(
                new AsposeWordContentExtractorFactory(),
                new POIWordContentExtractorFactory(),
                new ItextWordContentExtractorFactory()
        );

    }

    public static void main(String[] args) throws Exception {
        WordService wordService = new WordService();
        byte[] pdfBytes = wordService.convertToPdf(Files.readAllBytes(Paths.get("/Users/<USER>/Desktop/test.docx")),null);
        Files.write(Paths.get("/Users/<USER>/Desktop/test2.pdf"),pdfBytes);
    }

    private ExtractorUtil() {

    }

    /**
     * 根据配置获取提取器
     * @param properties
     * @return
     * @throws Exception
     */
    public static PageContentExtractor getPageContentExtractor(ExtractorProperties properties) throws Exception {
        ExtractConfigEnum typeEnum = properties.getTypeEnum();
        for (PageContentExtractorFactory provider : EXTRACTOR_FACTORIES) {
            if (provider.isSupport(typeEnum)) {
                log.debug("extractor's type is [{}] ", typeEnum);
                PageContentExtractor extractor = provider.getInstance();
                return extractor;
            }
        }
        throw new IllegalAccessException("unsupported type:" + typeEnum.name());
    }

    /**
     * 判断是否为word类型提取器
     * @param typeEnum
     * @return
     */
    public static boolean isWordType(ExtractConfigEnum typeEnum){
        return WORD_TYPE_ENUMS.contains(typeEnum);
    }
}
