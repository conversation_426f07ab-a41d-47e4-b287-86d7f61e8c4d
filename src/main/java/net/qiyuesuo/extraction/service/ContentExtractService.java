package net.qiyuesuo.extraction.service;

import net.qiyuesuo.extraction.config.ExtractOption;
import net.qiyuesuo.extraction.config.ExtractResult;

import java.io.File;

public interface ContentExtractService {

    /**
     * 提取指定页内容
     * @param file
     * @param pageNo
     * @return
     * @throws Exception
     */
    String getBodyContentFormPageNo(File file, int pageNo);
    String getHeaderContentFormPageNo(File file, int pageNo);
    String getFooterContentFormPageNo(File file, int pageNo);

    /**
     * 提取内容
     * @return
     * @throws Exception
     */
    ExtractResult getContent(ExtractOption option) throws Exception;

}
