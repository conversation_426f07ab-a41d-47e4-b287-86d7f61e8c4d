package net.qiyuesuo.extraction.service;

import lombok.extern.slf4j.Slf4j;
import net.qiyuesuo.extraction.config.ExtractOption;
import net.qiyuesuo.extraction.config.ExtractResult;
import net.qiyuesuo.extraction.config.enums.ExtractContentTypeEnum;
import net.qiyuesuo.extraction.config.properties.ExtractorProperties;
import net.qiyuesuo.extraction.extractor.PageContentExtractor;
import net.qiyuesuo.extraction.utils.ExtractorUtil;

import java.io.File;
import java.nio.file.Files;
import java.util.Collections;

@Slf4j
public class ContentExtractServiceImpl implements ContentExtractService {


    private ExtractorProperties properties;

    public ContentExtractServiceImpl(ExtractorProperties properties) {
        this.properties = properties;
    }

    @Override
    public ExtractResult getContent(ExtractOption option) throws Exception {
        return ExtractorUtil.getPageContentExtractor(properties).extractContent(option);
    }

    private ExtractResult getContentFormPageNo(byte[] bytes, int pageNo, ExtractContentTypeEnum contentType) throws Exception {
        PageContentExtractor extractor = ExtractorUtil.getPageContentExtractor(properties);
        ExtractOption option = ExtractOption.builder()
                .bytes(bytes)
                .pageNumbers(Collections.singletonList(pageNo))
                .body(ExtractContentTypeEnum.BODY.equals(contentType))
                .header(ExtractContentTypeEnum.HEADER.equals(contentType))
                .footer(ExtractContentTypeEnum.FOOTER.equals(contentType))
                .build();
        ExtractResult result = extractor.extractContent(option);
        return result;
    }

    @Override
    public String getBodyContentFormPageNo(File file, int pageNo) {
        try {
            ExtractResult result = this.getContentFormPageNo(Files.readAllBytes(file.toPath()), pageNo, ExtractContentTypeEnum.BODY);
            return result.getBodyTextFromPageNo(pageNo);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public String getHeaderContentFormPageNo(File file, int pageNo) {
        try {
            ExtractResult result = this.getContentFormPageNo(Files.readAllBytes(file.toPath()), pageNo, ExtractContentTypeEnum.HEADER);
            return result.getHeaderTextFromPageNo(pageNo);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public String getFooterContentFormPageNo(File file, int pageNo) {
        try {
            ExtractResult result = this.getContentFormPageNo(Files.readAllBytes(file.toPath()), pageNo, ExtractContentTypeEnum.FOOTER);
            return result.getFooterTextFromPageNo(pageNo);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


}
