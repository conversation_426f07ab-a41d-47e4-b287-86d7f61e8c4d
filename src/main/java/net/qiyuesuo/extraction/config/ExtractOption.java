package net.qiyuesuo.extraction.config;

import com.aspose.words.FindReplaceOptions;
import lombok.*;

import java.io.InputStream;
import java.util.List;

/**
 * 提取请求参数选项
 */
@Getter
@Setter
@Builder
public class ExtractOption {

    private byte[] bytes;

    private List<Integer> pageNumbers;

    private Boolean body;

    private Boolean header;

    private Boolean footer;

    private FindReplaceOptions replaceOptions;
}
