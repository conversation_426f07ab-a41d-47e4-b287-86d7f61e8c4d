package net.qiyuesuo.extraction.config.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.qiyuesuo.extraction.extractor.word.AsposeWordContentExtractor;
import net.qiyuesuo.extraction.extractor.word.ItextWordContentExtractor;
import net.qiyuesuo.extraction.extractor.word.POIWordContentExtractor;
import net.qiyuesuo.extraction.extractor.ContentExtractor;

/**
 * 第三方类库提取选择
 */
@Getter
@AllArgsConstructor
public enum ExtractConfigEnum {

    ASPOSE(AsposeWordContentExtractor.class),

    POI(POIWordContentExtractor.class),

    ITEXT(ItextWordContentExtractor.class);

    private final Class<? extends ContentExtractor> type;

}
