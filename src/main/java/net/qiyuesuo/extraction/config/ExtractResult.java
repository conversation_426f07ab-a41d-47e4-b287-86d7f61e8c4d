package net.qiyuesuo.extraction.config;

import lombok.Builder;
import net.qiyuesuo.extraction.config.enums.ExtractContentTypeEnum;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * 提取结果
 */
@Builder
public class ExtractResult {

    private Map<Integer, String> bodyTextMap;

    private Map<Integer, String> headerTextMap;

    private Map<Integer, String> footerTextMap;

    /**
     * 获取指定页的文本提取结果
     * @param pageNo 
     * @return
     */
    public String getBodyTextFromPageNo(int pageNo) {
        return this.getTextFromPageNo(pageNo, ExtractContentTypeEnum.BODY);
    }

    public String getHeaderTextFromPageNo(int pageNo) {
        return this.getTextFromPageNo(pageNo, ExtractContentTypeEnum.HEADER);
    }

    public String getFooterTextFromPageNo(int pageNo) {
        return this.getTextFromPageNo(pageNo, ExtractContentTypeEnum.FOOTER);
    }

    private String getTextFromPageNo(int pageNo, ExtractContentTypeEnum type) {
        switch (type) {
            case BODY:
                return Optional.ofNullable(bodyTextMap).orElse(new HashMap<>()).get(pageNo);
            case HEADER:
                return Optional.ofNullable(headerTextMap).orElse(new HashMap<>()).get(pageNo);
            case FOOTER:
                return Optional.ofNullable(footerTextMap).orElse(new HashMap<>()).get(pageNo);
            default:
                return null;
        }
    }

    public void addBodyText(int pageNo, String text) {
        this.addText(pageNo, text, ExtractContentTypeEnum.BODY);
    }

    public void addHeaderText(int pageNo, String text) {
        this.addText(pageNo, text, ExtractContentTypeEnum.HEADER);
    }

    public void addFooterText(int pageNo, String text) {
        this.addText(pageNo, text, ExtractContentTypeEnum.FOOTER);
    }

    private void addText(int pageNo, String text, ExtractContentTypeEnum type) {
        switch (type) {
            case BODY:
                Optional.ofNullable(bodyTextMap).orElse(new HashMap<>()).put(pageNo, text);
                break;
            case HEADER:
                Optional.ofNullable(headerTextMap).orElse(new HashMap<>()).put(pageNo, text);
                break;
            case FOOTER:
                Optional.ofNullable(footerTextMap).orElse(new HashMap<>()).put(pageNo, text);
                break;
        }
    }

}
