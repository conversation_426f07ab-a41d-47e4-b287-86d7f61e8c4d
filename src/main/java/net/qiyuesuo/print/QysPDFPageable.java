package net.qiyuesuo.print;

import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.common.PDRectangle;
import org.apache.pdfbox.printing.Orientation;
import org.apache.pdfbox.printing.Scaling;

import java.awt.print.Book;
import java.awt.print.PageFormat;
import java.awt.print.Paper;
import java.awt.print.Printable;

/**
 * Prints a PDF document using its original paper size.
 *
 * <AUTHOR>
 */
public class QysPDFPageable extends Book {
	
	private final PDDocument document;
	private final boolean showPageBorder;
	private final float dpi;
	private final Orientation orientation;

	private final int A4_WIDTH = 595;
	private final int A4_HEIGHT = 848;
	private final int SCALE_VALUE = 10;




	/**
	 * Creates a new PDFPageable.
	 *
	 * @param document the document to print
	 */
	public QysPDFPageable(PDDocument document) {
		this(document, Orientation.AUTO, false, 0);
	}

	/**
	 * Creates a new PDFPageable with the given page orientation.
	 *
	 * @param document the document to print
	 * @param orientation page orientation policy
	 */
	public QysPDFPageable(PDDocument document, Orientation orientation) {
		this(document, orientation, false, 0);
	}

	/**
	 * Creates a new PDFPageable with the given page orientation and with optional page borders
	 * shown. The image will be rasterized at the given DPI before being sent to the printer.
	 *
	 * @param document the document to print
	 * @param orientation page orientation policy
	 * @param showPageBorder true if page borders are to be printed
	 */
	public QysPDFPageable(PDDocument document, Orientation orientation, boolean showPageBorder) {
		this(document, orientation, showPageBorder, 0);
	}

	/**
	 * Creates a new PDFPageable with the given page orientation and with optional page borders
	 * shown. The image will be rasterized at the given DPI before being sent to the printer.
	 *
	 * @param document the document to print
	 * @param orientation page orientation policy
	 * @param showPageBorder true if page borders are to be printed
	 * @param dpi if non-zero then the image will be rasterized at the given DPI
	 */
	public QysPDFPageable(PDDocument document, Orientation orientation, boolean showPageBorder, float dpi) {
		this.document = document;
		this.orientation = orientation;
		this.showPageBorder = showPageBorder;
		this.dpi = dpi;
	}

	@Override
	public int getNumberOfPages() {
		return document.getNumberOfPages();
	}

}
