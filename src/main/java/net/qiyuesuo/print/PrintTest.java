package net.qiyuesuo.print;

import com.itextpdf.text.Document;
import com.itextpdf.text.pdf.PdfReader;
import net.qiyuesuo.common.io.IOUtils;
import org.apache.pdfbox.Loader;
import org.apache.pdfbox.pdmodel.PDDocument;

import javax.print.Doc;
import javax.print.DocFlavor;
import javax.print.DocPrintJob;
import javax.print.PrintException;
import javax.print.PrintService;
import javax.print.PrintServiceLookup;
import javax.print.SimpleDoc;
import javax.print.attribute.Attribute;
import javax.print.attribute.AttributeSet;
import javax.print.attribute.HashPrintRequestAttributeSet;
import javax.print.attribute.PrintRequestAttributeSet;
import javax.print.attribute.standard.Copies;
import javax.print.attribute.standard.MediaSizeName;
import javax.print.attribute.standard.Sides;
import java.awt.print.PrinterJob;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

public class PrintTest {

	public static void main(String[] args) throws PrintException, IOException {
		String filePath = "/Users/<USER>/Downloads/printtest.pdf";

		String[] imagePaths = {
				"/Users/<USER>/Downloads/合同比对.png",
				"/Users/<USER>/Downloads/企业监测.png"
		};  // 替换为您的图片路径
//		duplexAttribute();
		printImages(imagePaths);
//		printFile(filePath);
//		printPdf_itext(filePath);
	}

	public static void printImages(String[] imagePaths) {
		PrintRequestAttributeSet attributes = new HashPrintRequestAttributeSet();
		attributes.add(new Copies(1));  // 设置打印份数
		attributes.add(MediaSizeName.ISO_A4);  // 设置纸张大小（）
		attributes.add(Sides.DUPLEX);  // 设置双面打印


		PrintService defaultPrintService = lookupByName(DocFlavor.INPUT_STREAM.AUTOSENSE, "契约锁");
//		PrintService defaultPrintService = PrintServiceLookup.lookupDefaultPrintService();
		if (defaultPrintService == null) {
			System.out.println("未找到默认打印服务");
			return;
		}

		for (String imagePath : imagePaths) {
			try {
				DocPrintJob printJob = defaultPrintService.createPrintJob();
				DocFlavor flavor = DocFlavor.INPUT_STREAM.JPEG;
				File file = new File(imagePath);
				InputStream inputStream = Files.newInputStream(file.toPath());
				Doc doc = new SimpleDoc(inputStream, flavor, null);
				printJob.print(doc, attributes);
			} catch (IOException | PrintException e) {
				e.printStackTrace();
			}
		}
	}

	public static void printFile(String filePath) throws PrintException, IOException {
		filePath = "/Users/<USER>/Downloads/ofdtopdf.pdf";
		Path path = Paths.get(filePath);
		byte[] bytes =  Files.readAllBytes(path);

		PrintRequestAttributeSet attributes = new HashPrintRequestAttributeSet();
		attributes.add(new Copies(1));  // 设置打印份数
//		attributes.add(MediaSizeName.ISO_A4);  // 设置纸张大小
		attributes.add(Sides.DUPLEX);  // 设置双面打印

		PrintService printService = lookupByName(DocFlavor.INPUT_STREAM.AUTOSENSE, "契约锁");
		PDDocument doc = null;
		try {
			doc = Loader.loadPDF(bytes);
			PrinterJob job = PrinterJob.getPrinterJob();
			job.setPageable(new QysPDFPageable(doc));
			job.setPrintService(printService);

			job.print(attributes);
		}catch (Exception e) {
			e.printStackTrace();
			throw new PrintException(e.getMessage());
		}finally {
			IOUtils.safeClose(doc);
			//置为null，gc回收
			doc=null;
		}
	}


	public static void printPdf_itext(String pdfFilePath) {
		PrintRequestAttributeSet attributes = new HashPrintRequestAttributeSet();
		attributes.add(new Copies(1));  // 设置打印份数
//		attributes.add(MediaSizeName.ISO_A4);  // 设置纸张大小
		attributes.add(Sides.DUPLEX);  // 设置双面打印

		PrintService printService =lookupByName(DocFlavor.INPUT_STREAM.AUTOSENSE, "契约锁");

		PdfReader reader = null;
		try {
			reader = new PdfReader(pdfFilePath);
			Document document = new Document();
			DocPrintJob printJob = printService.createPrintJob();
			DocFlavor flavor = DocFlavor.INPUT_STREAM.PDF;
			SimpleDoc doc = new SimpleDoc(reader.getMetadata(), flavor, null);
			printJob.print(doc, attributes);
		} catch (Exception e) {
			e.printStackTrace();
		}finally {
			if(reader != null) {
				reader.close();
			}
		}
	}

	public static void duplexAttribute() {
		PrintService defaultPrintService = lookupByName(DocFlavor.INPUT_STREAM.AUTOSENSE, "契约锁");
		if (defaultPrintService!= null) {
			AttributeSet attributes = defaultPrintService.getAttributes();
			Attribute duplexAttribute = attributes.get(Sides.class);
			if (duplexAttribute!= null) {
				if (duplexAttribute.equals(Sides.DUPLEX))  {
					System.out.println(" 打印机支持自动双面打印");
				} else {
					System.out.println(" 打印机不支持自动双面打印");
				}
			}
		}
	}


	/**
	 * 根据名称获取本地打印机
	 * @param flavor
	 * @param name
	 * @return
	 */
	public static PrintService lookupByName(DocFlavor flavor, String name) {
		PrintRequestAttributeSet pras = new HashPrintRequestAttributeSet();
		if(name == null || name.equals("") ) {
			return null;
		}
		// 可用的打印机列表(字符串数组)
		PrintService[] printService = PrintServiceLookup.lookupPrintServices(flavor, pras);
		for (PrintService ps : printService) {
			if(name.equals(ps.getName())){
				return ps;
			}
		}
		return null;
	}
}