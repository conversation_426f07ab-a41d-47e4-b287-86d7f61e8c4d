//package net.qiyuesuo.ofd;
//
//import net.qiyuesuo.common.pdf.exception.ConvertorException;
//import org.ofdrw.converter.ConvertHelper;
//import org.ofdrw.converter.ImageMaker;
//import org.ofdrw.layout.OFDDoc;
//import org.ofdrw.layout.element.Paragraph;
//import org.ofdrw.reader.OFDReader;
//
//import javax.imageio.ImageIO;
//import java.awt.image.BufferedImage;
//import java.io.ByteArrayInputStream;
//import java.io.FileInputStream;
//import java.io.IOException;
//import java.io.InputStream;
//import java.nio.file.Path;
//import java.nio.file.Paths;
//import java.util.Map;
//
//public class HelloWorld {
//	public static void main(String[] args) throws IOException, ConvertorException {
////		Path path = Paths.get("HelloWorld.ofd");
////		try (OFDDoc ofdDoc = new OFDDoc(path)) {
////			Paragraph p = new Paragraph("你好呀，OFD Reader&Writer！");
////			ofdDoc.add(p);
////		}
////		System.out.println("生成文档位置: " + path.toAbsolutePath());
//
////		try {
////			BufferedImage bufferedImage = HelloWorld.getImageByKey();
////			String a = "end";
////		} catch (ConvertorException e) {
////			throw new RuntimeException(e);
////		}
//
//		topdf();
//	}
//
//	public static BufferedImage getImageByKey() throws ConvertorException {
//		String ofdfile = "/Users/<USER>/4工作/6test/ofd测试/测试可用.ofd";
//		try (InputStream inputStream = new FileInputStream(ofdfile)){
//			OFDReader reader = new OFDReader(inputStream);
//			ImageMaker maker = new ImageMaker(reader,9);
//			return maker.makePage(2 - 1);
//		}catch (Exception e){
//			e.printStackTrace();
//		}
//		return null;
//	}
//
//	public static void topdf() throws ConvertorException {
//		// 源OFD文件路径，这里替换成你实际的OFD文件路径
//		String ofdFilePath = "/Users/<USER>/4工作/6test/ofd测试/测试可用.ofd";
//		// 目标PDF文件路径，这里替换成你想要输出的PDF文件路径
//		String pdfFilePath = "/Users/<USER>/4工作/6test/ofd测试/测试可用.pdf";
//
//		java.io.File pdfFile = new java.io.File(pdfFilePath);
//
//		try (InputStream inputStream = new FileInputStream(ofdFilePath)){
//			// 调用ofdrw的转换方法进行转换
//			ConvertHelper.toPdf(inputStream, pdfFile);
//			System.out.println("OFD文件转换为PDF文件成功！");
//		} catch (IOException e) {
//			System.err.println("转换过程出现错误：" + e.getMessage());
//			e.printStackTrace();
//		}
//	}
//}
