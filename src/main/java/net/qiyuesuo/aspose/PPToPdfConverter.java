//package net.qiyuesuo.aspose;
//
//import com.aspose.slides.Presentation;
//import com.aspose.slides.SaveFormat;
//import com.aspose.words.Document;
//
//
//import java.io.ByteArrayOutputStream;
//import java.io.InputStream;
//
//public class PPToPdfConverter {
//
//	public static void main(String[] args) {
//		// 输入的Word文件路径
////		String inputWordFilePath = "/Users/<USER>/4工作/6test/异常文件/文档中图片转换后失真aspose.docx";
////		// 输出的PDF文件路径
////		String outputPdfFilePath = "/Users/<USER>/4工作/6test/异常文件/文档中图片转换后失真aspose.pdf";
//
//		String inputWordFilePath = "/Users/<USER>/4工作/6test/异常文件/testppt内存溢出.pptx";
//		String outputPdfFilePath = "/Users/<USER>/4工作/6test/异常文件/testppt内存溢出.pdf";
//
//		Presentation doc = null;
//		try {
//			// 加载Word文档
//			doc = new Presentation(inputWordFilePath);
//
//			// 将文档保存为PDF格式
//			doc.save(outputPdfFilePath, SaveFormat.Pdf);
//
//			System.out.println("Word文件已成功转换为PDF文件。");
//		} catch (Exception e) {
//			e.printStackTrace();
//		} finally {
//			if(doc != null){
//				doc.dispose();
//			}
//		}
//	}
//}
