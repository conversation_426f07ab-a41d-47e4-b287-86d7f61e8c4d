package net.qiyuesuo.aspose.confiden;

import net.qiyuesuo.common.word.confiden.ConfidentialException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Cipher;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.function.Function;

public class Confidential {

	Logger logger = LoggerFactory.getLogger(Confidential.class);
	private static String BASE_SECRET = "5f6db7ec8325a5e4";
	private static String ENCRYPT_PREFIX = "{cipher}";

	/**
	 * 密钥生成器
	 */
	Function<String, SecretKeySpec> SECRETKEY_SUPPLIER = salt -> {
		try {
			Mac mac = Mac.getInstance("HmacSHA256");
			SecretKeySpec baseSecret = new SecretKeySpec(BASE_SECRET.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
			mac.init(baseSecret);
			return new SecretKeySpec(mac.doFinal(salt.getBytes(StandardCharsets.UTF_8)), "AES");
		} catch (Exception e) {
			throw new ConfidentialException(e);
		}
	};
	/**
	 * 加密
	 */
	public String encrypt(String data, String salt) {
		if (null == data || "".equals(data)) {
			return data;
		}
		if (data.startsWith(ENCRYPT_PREFIX)) {
			return data;
		}

		try {
			Cipher cipher = Cipher.getInstance("AES");
			cipher.init(Cipher.ENCRYPT_MODE, SECRETKEY_SUPPLIER.apply(salt));
			return ENCRYPT_PREFIX + new String(Base64.getEncoder().encode(cipher.doFinal(data.getBytes(StandardCharsets.UTF_8))), StandardCharsets.UTF_8);
		} catch (Exception e) {
			logger.error("加密失败", e);
			return data;
		}

	}

	/**
	 * 解密
	 */
	public String decrypt(String data,String salt) {
		if (null == data || "".equals(data)) {
			return data;
		}
		if (!data.startsWith(ENCRYPT_PREFIX)) {
			return data;
		}
		if (null == salt || "".equals(salt)) {
			return data;
		}
		try {
			data = data.substring(ENCRYPT_PREFIX.length());
			Cipher cipher = Cipher.getInstance("AES");
			cipher.init(Cipher.DECRYPT_MODE, SECRETKEY_SUPPLIER.apply(salt));
			byte[] result = cipher.doFinal(Base64.getDecoder().decode(data.getBytes(StandardCharsets.UTF_8)));
			return new String(result, StandardCharsets.UTF_8);
		} catch (Exception e) {
			logger.error("解密失败", e);
			return data;
		}
	}



}
