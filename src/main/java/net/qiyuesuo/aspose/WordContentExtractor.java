package net.qiyuesuo.aspose;

import com.aspose.words.Cell;
import com.aspose.words.Document;
import com.aspose.words.LayoutCollector;
import com.aspose.words.Node;
import com.aspose.words.NodeType;
import com.aspose.words.PageInfo;
import com.aspose.words.Paragraph;
import com.aspose.words.Row;
import com.aspose.words.SaveFormat;
import com.aspose.words.Shape;
import com.aspose.words.Table;
import com.aspose.words.VisitorAction;
import com.aspose.words.DocumentVisitor;
import java.io.FileOutputStream;
import java.io.IOException;

public class WordContentExtractor {
	public static void main(String[] args) {
		try {
			// 设置商业许可证（试用版会加水印）
//			License license = new License();
//			license.setLicense("Aspose.Words.Java.lic");

			// 输入文件路径和目标页码（页码从1开始）
			String inputPath = "/Users/<USER>/Downloads/台州银行行政电子印章系统设计说明书.doc";

			int targetPage = 3; // 要提取的第二页

			// 执行提取
			String content = extractPageContent(inputPath, targetPage);
			System.out.println("第 " + targetPage + " 页内容：\n" + content);

		} catch (Exception e) {
			System.err.println("提取失败: " + e.getMessage());
		}
	}

	public static String extractPageContent(String filePath, int pageNumber) throws Exception {
		Document doc = new Document(filePath);
		LayoutCollector collector = new LayoutCollector(doc);

		final StringBuilder pageContent = new StringBuilder();

		// 使用 DocumentVisitor 替代 NodeVisitor
		DocumentVisitor visitor = new DocumentVisitor() {
			@Override
			public int visitParagraphStart(Paragraph paragraph) throws Exception {
				processElement(paragraph);
				return VisitorAction.CONTINUE;
			}

			@Override
			public int visitTableStart(Table table) throws Exception {
				processElement(table);
				return VisitorAction.CONTINUE;
			}

			@Override
			public int visitShapeStart(Shape shape) throws Exception {
				processElement(shape);
				return VisitorAction.CONTINUE;
			}

			private void processElement(Node node) throws Exception {
				int startPage = collector.getStartPageIndex(node);
				int endPage = collector.getEndPageIndex(node);

				if (pageNumber - 1 >= startPage && pageNumber - 1 <= endPage) {
					extractContent(node);
				}
			}

			private void extractContent(Node node) {
				switch (node.getNodeType()) {
					case NodeType.PARAGRAPH:
						pageContent.append(((Paragraph) node).getText()).append("\n");
						break;
					case NodeType.TABLE:
						pageContent.append(convertTable((Table) node));
						break;
					case NodeType.SHAPE:
						pageContent.append("[图形: ")
								.append(((Shape) node).getAlternativeText())
								.append("]\n");
						break;
				}
			}

			private String convertTable(Table table) {
				StringBuilder sb = new StringBuilder("\n");
				for (Row row : table.getRows()) {
					for (Cell cell : row.getCells()) {
						sb.append(cell.getText().trim()).append(" | ");
					}
					sb.append("\n");
				}
				return sb.toString();
			}
		};

		doc.accept(visitor);  // 正确触发遍历
		return pageContent.toString();
	}
}
