package net.qiyuesuo.aspose;

import com.aspose.pdf.Document;
import com.aspose.pdf.Page;
import com.aspose.pdf.TextAbsorber;
import com.aspose.pdf.TextSearchOptions;

public class PdfContentExtractor {
	public static void main(String[] args) {

		// 输入 PDF 文件的路径
		String inputFilePath = "/Users/<USER>/Downloads/二手车检测评估协议.pdf";
		Document pdfDocument = new Document(inputFilePath);

		// 指定要提取内容的页码（页码从 1 开始）
		int pageNumber = 2;

		// 获取指定页码的页面
		Page page = pdfDocument.getPages().get_Item(pageNumber);

		// 创建 TextAbsorber 对象以提取文本
		TextAbsorber textAbsorber = new TextAbsorber();

		// 设置文本搜索选项，允许按顺序提取文本
		TextSearchOptions textSearchOptions = new TextSearchOptions(true);
		textAbsorber.setTextSearchOptions(textSearchOptions);

		// 接受 absorber 处理该页面
		page.accept(textAbsorber);

		// 获取提取的文本内容
		String extractedText = textAbsorber.getText();

		// 处理换行符，将换行符替换为系统默认的换行符
		String processedText = extractedText.replaceAll("\\r\\n|\\r|\\n", System.lineSeparator());

		// 输出提取的文本
		System.out.println("第 " + pageNumber + " 页的内容为：");
		System.out.println(processedText);

		// 关闭文档
		pdfDocument.close();
	}
}