package net.qiyuesuo.aspose;

import com.aspose.words.Document;
import com.aspose.words.License;
import com.aspose.words.SaveFormat;
import net.qiyuesuo.common.word.confiden.Confidential;

import java.io.ByteArrayInputStream;

public class WordToPdfConverter {
	public static String licenseString = "{cipher}Xk2OU6s3eAgubvZlSTUla6i1aEYjkajbjdBZDgfhgde9g7GKXYQJeqx3GNSp3vYm7OOa7lkZgda+K3VxkKtyQO1ldjHAO42eU3HwiEQYGVxQUZ96gUqYhwpbstp3dhRL0OFak+B8Zhxv+o+JReN8CxFYDcQ12TbYATrXFoqlWJWhm9VzWltJoSedjZaWbP9w7a3K0Rp+jWRs6a5CRnt2gN0XrVYNI2vHj8WqyNwrh++ZbBXwQrBC8peUV4XYf1SvnFVl+9EerzwOkXL+XusA7k7HTkHuIhFNzWx1swseU+PdcwCW1RehWiAFVDvUKynEgEkazCMvq2Zrhjg49SqQYm1FlvC22drGlx3MwLxFSLpJflNYZIofTUlDyly315p9e7Icpud4KpexLNuoyzeKwoSvyOot3SqWBpcoOPZfREsZF9W7ZzF6XQbt1DI7Iu9G7jyP4iZdNFzk0AaFow4xZ/gYG6y5lSGLKyaziXTVECL3cSot2bVmI5DRuIMBcn9LCsNT20aYBhJssdROtrwYkqGc3kccear5TGSiJ/vZFcWWNCkd6InGZEMfI4BSry2Xv8p1CB/Fa1K+oXeggCXCAlLSoMkBmqKqwHGpzMFckARRCjl1fkZHA5rds+htqej8KRI07QEk+N9Xn2qZJRL0hYeEHFrsQtjlds9jjbszQRYcNJUy4FqeQayujWRruBn3teDeJyBT5w1ZIArmUxoOgRtWM/m7obSyXSmF8yNswqomVdWcN2uCJL3ySPrhYEFEZGpBLLRzzgVOlOXGLVLxI5gL2Xym5dnEXHDpVi0AyhsGYIhEQeVvbsowDoInd174I8qGkZeJgMwEbV0No59tIv7/XFEUt/omxlpkGgLi6d3U/3ElivkkfSuMJOtpOHToxOJoeX9OLKzdiJ4kuPtZVRpeJ4nY3rt+L/ZP8Sqb4NoaE/bzHHk5Ay4Ap3BvxiOK1zHdVdiI1XG0Ax6Zjg379FV4yKfAEXFXqX4VFYkJpMtf0N+NHyW4mzPQfSV+nrdAHo7XnkxzU9qXbaehSut0bgntNTcdAbb4wxlGkCT7dzB+ChP2gD6I+OOiJ6ajP/0aDjV/M8pYjbsaV7REUfYcecxKa0BEM5X33xEREF1zFj/AFOB52Y0izOGICf+EhhGjeKZke1p/2ux4oUvvk3Aoa4eDaqJ76GYJ0+nMt9o2AGd/N4RLvNeF4yz8uNJJ8eIzJg8l8+d5iiQdTjufm/Iaroujz0eITaghRP0NttZO0YLL5rfC32egamxyoEluA08EIQDsTN+bXweOoNc5DYgih4BYoq5RlzwZcTiq/UTDixM=";
	public static String salt = "net/qiyuesuo";

	public static void main(String[] args) {
		// 输入的Word文件路径
//		String inputWordFilePath = "/Users/<USER>/4工作/6test/异常文件/文档中图片转换后失真aspose.docx";
//		// 输出的PDF文件路径
//		String outputPdfFilePath = "/Users/<USER>/4工作/6test/异常文件/文档中图片转换后失真aspose.pdf";

//		String inputWordFilePath = "/Users/<USER>/4工作/6test/异常文件/apose238转换多一页.doc";
//		String outputPdfFilePath = "/Users/<USER>/4工作/6test/异常文件/apose238转换多一页.pdf";

		String inputWordFilePath = "/Users/<USER>/4工作/6test/异常文件/附件的地方页码有误.docx";
		String outputPdfFilePath = "/Users/<USER>/4工作/6test/异常文件/附件的地方页码有误.pdf";


		try {
			setLicence();
			// 加载Word文档
			Document doc = new Document(inputWordFilePath);

			// 将文档保存为PDF格式
			doc.save(outputPdfFilePath, SaveFormat.PDF);

			System.out.println("Word文件已成功转换为PDF文件。");
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private static void setLicence() {
		Confidential confidential = new Confidential();
		License license = new License();
		try {
			String newLicence = confidential.decrypt(licenseString, salt);
			license.setLicense(new ByteArrayInputStream(newLicence.getBytes()));
		} catch (Exception e) {

		}
	}
}