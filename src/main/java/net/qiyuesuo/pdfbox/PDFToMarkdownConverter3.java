package net.qiyuesuo.pdfbox;

import org.apache.pdfbox.Loader;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.regex.*;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;
import org.apache.pdfbox.text.TextPosition;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.regex.*;

public class PDFToMarkdownConverter3 {
	// 使用最新 PDFBox 3.0.1
	private static final String PDFBOX_VERSION = "3.0.1";

	// 中文标题正则（支持 1、 1.1 等格式）
	private static final Pattern TITLE_PATTERN = Pattern.compile(
			"^([\\d]+[、.]?[\\d.]*)\\s+(.*?)$"
	);

	// 表格检测正则（匹配字段名称行）
	private static final Pattern TABLE_HEADER_PATTERN = Pattern.compile(
			"字段名称\\s+字段类型及长度"
	);

	// 代码块标识（Java 包路径）
	private static final Pattern CODE_PATTERN = Pattern.compile(
			"^com/(.*?)\\.java$"
	);

	public static void main(String[] args) {
		String inputPdf = "/Users/<USER>/Downloads/台州银行行政电子印章系统设计说明书.pdf";
		String outputMd = "/Users/<USER>/Downloads/台州银行行政电子印章系统设计说明书.md";

		try (PDDocument document = Loader.loadPDF(new File(inputPdf));
			 BufferedWriter writer = new BufferedWriter(
					 new OutputStreamWriter(new FileOutputStream(outputMd), StandardCharsets.UTF_8))) {

			// 增强型 PDF 解析器（处理中文换行）
			PDFTextStripper stripper = new PDFTextStripper() {
				private String lastLine = "";

				@Override
				public String getText(PDDocument doc) throws IOException {
					String rawText = super.getText(doc);
					// 合并被错误分割的中文字符
					return rawText.replaceAll("(?<![\\r\\n])\\s+(?=[\\u4e00-\\u9fa5])", "");
				}

			};

			stripper.setSortByPosition(true);
			String text = stripper.getText(document);

			// 分块处理文本
			List<String> blocks = Arrays.asList(text.split("\\n\\s*\\n"));

			boolean inTable = false;
			List<String> tableRows = new ArrayList<>();

			for (String block : blocks) {
				block = block.trim();
				if (block.isEmpty()) continue;

				// 处理标题
				Matcher titleMatcher = TITLE_PATTERN.matcher(block);
				if (titleMatcher.find()) {
					String levelMark = getTitleLevel(titleMatcher.group(1));
					writer.write(levelMark + " " + titleMatcher.group(2) + "\n\n");
					continue;
				}

				// 处理表格
				if (TABLE_HEADER_PATTERN.matcher(block).find()) {
					inTable = true;
					tableRows.clear();
					writer.write("| 字段名称 | 字段类型及长度 | 数值有效范围列表 | 输入输出方式(I/O) | 是否必输 | 字段处理说明 | 备注 |\n");
					writer.write("|----------|----------------|------------------|-------------------|----------|--------------|------|\n");
					continue;
				}

				if (inTable) {
					if (block.contains("-----")) {
						inTable = false;
						tableRows.forEach(row -> {
							try {
								writer.write(formatTableRow(row) + "\n");
							} catch (IOException e) {
								e.printStackTrace();
							}
						});
						writer.write("\n");
						continue;
					}
					tableRows.add(block);
				}

				// 处理代码块
				Matcher codeMatcher = CODE_PATTERN.matcher(block);
				if (codeMatcher.find()) {
					writer.write("```java\n");
					writer.write(block + "\n");
					writer.write("```\n\n");
					continue;
				}

				// 处理普通段落
				writer.write(block + "\n\n");
			}

		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private static String getTitleLevel(String numbering) {
		int depth = numbering.split("[、.]").length;
		return repeat("#", Math.max(1, depth));
	}

	private static String formatTableRow(String row) {
		// 增强型表格列分割（支持中文空格）
		String[] cols = row.split("\\s{2,}");
		if (cols.length != 7) return "";
		return String.format("| %s | %s | %s | %s | %s | %s | %s |",
				cols[0], cols[1], cols[2], cols[3], cols[4], cols[5], cols[6]);
	}

	private static String repeat(String str, int count) {
		StringBuilder sb = new StringBuilder();
		for (int i = 0; i < count; i++) {
			sb.append(str);
		}
		return sb.toString();
	}
}
