package net.qiyuesuo.pdfbox;

import org.apache.pdfbox.Loader;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageTree;
import org.apache.pdfbox.pdmodel.graphics.image.PDImageXObject;
import org.apache.pdfbox.rendering.PDFRenderer;
import org.apache.pdfbox.text.PDFTextStripper;
import net.sourceforge.tess4j.Tesseract;
import net.sourceforge.tess4j.TesseractException;

import java.awt.image.BufferedImageOp;
import java.awt.image.ConvolveOp;
import java.awt.image.Kernel;
import java.io.File;
import java.io.FileWriter;
import java.awt.image.BufferedImage;
import java.util.ArrayList;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class PdfToTextWithOCR {
	public static void main(String[] args) {
		try {
			String pdfPath = "src/test/resources/file/test1.pdf";
			String outputPath = "src/test/resources/file/test1.txt";
			convertPdfToTextWithOCR(pdfPath, outputPath);

			System.out.println("转换完成！");
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public static void convertPdfToTextWithOCR(String pdfPath, String outputPath) throws Exception {
		try (PDDocument document = Loader.loadPDF(new File(pdfPath))) {
			PDFRenderer renderer = new PDFRenderer(document);
			// 设置PDF渲染参数
			renderer.setSubsamplingAllowed(true);    // 启用子采样
			renderer.setImageDownscalingOptimizationThreshold(0.5f); // 图像缩放阈值

			Tesseract tesseract = new Tesseract();
			// 扫描件优化配置（效果较好
			tesseract.setPageSegMode(3); // 自动页面分割
			tesseract.setOcrEngineMode(3); // 使用LSTM引擎
			tesseract.setDatapath("src/main/resources/tessdata");
			tesseract.setLanguage("eng+chi_sim"); // 支持中英文识别

			FileWriter writer = new FileWriter(outputPath);

			PDPageTree pages = document.getPages();
			for (int pageNum = 0; pageNum < pages.getCount(); pageNum++) {
				// 提取文本
				String text = new PDFTextStripper().getText(document);
				writer.write(text + "\n\n");

				// 提取并识别图片
				BufferedImage image = renderer.renderImage(pageNum);
//				PDImageXObject pdImage = PDImageXObject.createFromFile("", document);
				String ocrText = tesseract.doOCR(image);
				writer.write("[图片识别内容]\n" + ocrText + "\n\n");
			}

			writer.close();
		} catch (TesseractException e) {
			if (e.getMessage().contains("Error opening data file")) {
				System.err.println("语言包路径配置错误，请检查：");
				System.err.println("1. TESSDATA_PREFIX环境变量设置");
				System.err.println("2. tessdata目录存在性");
			} else if (e.getMessage().contains("Image not readable")) {
				System.err.println("图片预处理失败，尝试：");
				System.err.println("1. 转换为RGB格式");
				System.err.println("2. 调整DPI设置");
			}
		}
	}

	//图片预处理增强
	private static BufferedImage preprocessImage(BufferedImage image) {
		// 转换为灰度图
		BufferedImage grayImage = new BufferedImage(
				image.getWidth(),
				image.getHeight(),
				BufferedImage.TYPE_BYTE_GRAY
		);
		grayImage.getGraphics().drawImage(image, 0, 0, null);

		// 锐化处理
		float[] sharpenMatrix = { 0, -1, 0, -1, 5, -1, 0, -1, 0 };
		BufferedImageOp sharpenFilter = new ConvolveOp(
				new Kernel(3, 3, sharpenMatrix)
		);
		return sharpenFilter.filter(grayImage, null);
	}

	//多线程处理
//	ExecutorService executor = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors());
//	List<Future<String>> futures = new ArrayList<>();
//	for (int pageNum = 0; pageNum < totalPages; pageNum++) {
//		final int currentPage = pageNum;
//		futures.add(executor.submit(() -> {
//			BufferedImage image = renderer.renderImage(currentPage);
//			return tesseract.doOCR(preprocessImage(image));
//		}));
//	}
//
//	for (Future<String> future : futures) {
//		writer.write(future.get());
//	}

}
