package net.qiyuesuo.pdfbox;

import org.apache.pdfbox.Loader;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;
import org.apache.pdfbox.text.TextPosition;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.regex.*;

public class ChinesePDFToMarkdown {
	private static final Pattern CN_HEADER = Pattern.compile("^(\\d+[、.]\\d*)\\s+(.*)");
	private static final Pattern CN_TABLE_HEADER = Pattern.compile("字段名称\\s+字段类型及长度");
	private static final Pattern CN_TABLE_ROW = Pattern.compile("(.+?)\\s{2,}(.+?)\\s{2,}(.+?)\\s{2,}(.+?)\\s{2,}(.+?)\\s{2,}(.+?)\\s{2,}(.*)");

	public static void main(String[] args) {
		String inputPdf = "/Users/<USER>/Downloads/台州银行行政电子印章系统设计说明书.pdf";
		String outputMd = "/Users/<USER>/Downloads/台州银行行政电子印章系统设计说明书.md";


		try (PDDocument doc = Loader.loadPDF(new File(inputPdf));
			 BufferedWriter writer = new BufferedWriter(
					 new OutputStreamWriter(new FileOutputStream(outputMd), "UTF-8"))) {

			PDFTextStripper stripper = new PDFTextStripper(){
				// 优化中文换行处理
				@Override
				protected void writeString(String text, List<TextPosition> textPositions) throws IOException {
					StringBuilder builder = new StringBuilder();
					for (TextPosition position : textPositions) {
						String c = position.getUnicode();
						builder.append(c);
						// 合并被错误分割的汉字
						if (c.matches("[\u4e00-\u9fa5]")) {
							builder.append(" ");
						}
					}
					writeString(builder.toString());
				}
			};

			String text = stripper.getText(doc);
			boolean inTable = false;
			int tableColCount = 0;

			for (String line : text.split("\\r?\\n")) {
				line = line.trim();

				// 中文标题处理
				Matcher headerMatcher = CN_HEADER.matcher(line);
				if (headerMatcher.find()) {
					String number = headerMatcher.group(1);
					String title = headerMatcher.group(2);
					int level = number.split("[、.]").length;
					writer.write(repeat("#", level) + " " + title + "\n\n");

					continue;
				}

				// 中文表格处理
				if (CN_TABLE_HEADER.matcher(line).find()) {
					inTable = true;
					tableColCount = 7; // 根据字段数设置
					writer.write("| 字段名称 | 字段类型及长度 | 数值有效范围列表 | 输入输出方式(I/O) | 是否必输 | 字段处理说明 | 备注 |\n");
					writer.write("|----------|----------------|------------------|-------------------|----------|--------------|------|\n");
					continue;
				}

				if (inTable) {
					Matcher rowMatcher = CN_TABLE_ROW.matcher(line);
					if (rowMatcher.matches() && rowMatcher.groupCount() == tableColCount) {
						writer.write("| " + String.join(" | ",
								rowMatcher.group(1),
								rowMatcher.group(2),
								rowMatcher.group(3),
								rowMatcher.group(4),
								rowMatcher.group(5),
								rowMatcher.group(6),
								rowMatcher.group(7)) + " |\n");
					} else {
						inTable = false;
						writer.write("\n");
					}
					continue;
				}

				// 程序文件清单处理
				if (line.startsWith("com/")) {
					writer.write("```java\n" + line + "\n```\n\n");
					continue;
				}

				// 普通段落处理
				if (!line.isEmpty()) {
					writer.write(line + "\n\n");
				}
			}

			System.out.println("转换完成，输出文件：" + outputMd);

		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	private static String repeat(String str, int count) {
		StringBuilder sb = new StringBuilder();
		for (int i = 0; i < count; i++) {
			sb.append(str);
		}
		return sb.toString();
	}
}

