package net.qiyuesuo.pdfbox;

import org.apache.pdfbox.Loader;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class PdfToMarkdownConverter2 {

	public static void main(String[] args) {
		String pdfFilePath = "/Users/<USER>/Downloads/台州银行行政电子印章系统设计说明书.pdf";
		String markdownFilePath = "/Users/<USER>/Downloads/台州银行行政电子印章系统设计说明书.md";
		try {
			FileWriter writer = new FileWriter(markdownFilePath);
			String markdownContent = convertPdfToMarkdown(pdfFilePath);
			writer.write(markdownContent);
			System.out.println(markdownContent);
		} catch (IOException e) {
			e.printStackTrace();
		}
	}

	public static String convertPdfToMarkdown(String pdfFilePath) throws IOException {
		String pdfText = extractTextFromPdf(pdfFilePath);
		StringBuilder markdown = new StringBuilder();
		String[] lines = pdfText.split("\n");
		List<String> tableLines = new ArrayList<>();
		boolean inTable = false;

		for (int i = 0; i < lines.length; i++) {
			String line = lines[i].trim();

			// 处理标题
			int titleLevel = detectTitleLevel(line);
			if (titleLevel > 0) {
				String titleText = extractTitleText(line, titleLevel);
				markdown.append(repeat("#", titleLevel)).append(" ").append(titleText).append("\n\n");
				continue;
			}

			// 处理列表
			String listItem = detectListItem(line);
			if (listItem != null) {
				markdown.append(listItem).append("\n");
				continue;
			}

			// 处理表格
			if (looksLikeTableLine(line)) {
				if (!inTable) {
					inTable = true;
				}
				tableLines.add(line);
				if (i + 1 < lines.length && looksLikeTableSeparator(lines[i + 1])) {
					tableLines.add(lines[++i]);
					for (String tableLine : tableLines) {
						markdown.append(tableLine).append("\n");
					}
					markdown.append("\n");
					tableLines.clear();
					inTable = false;
				}
				continue;
			}

			// 处理段落
			if (inTable) {
				inTable = false;
				if (!tableLines.isEmpty()) {
					for (String tableLine : tableLines) {
						markdown.append(tableLine).append("\n");
					}
					markdown.append("\n");
					tableLines.clear();
				}
			}
			line = processLinksAndImages(line);
			if (!line.isEmpty()) {
				markdown.append(line).append("\n\n");
			}
		}

		return markdown.toString();
	}

	private static String extractTextFromPdf(String pdfFilePath) throws IOException {
		try (PDDocument document = Loader.loadPDF(new File(pdfFilePath))) {
			PDFTextStripper pdfStripper = new PDFTextStripper();
			return pdfStripper.getText(document);
		}
	}

	private static String processLinksAndImages(String line) {
		// 处理链接
		Pattern linkPattern = Pattern.compile("\\[(.*?)\\]\\((.*?)\\)");
		Matcher linkMatcher = linkPattern.matcher(line);
		while (linkMatcher.find()) {
			String linkText = linkMatcher.group(1);
			String linkUrl = linkMatcher.group(2);
			line = line.replace(linkMatcher.group(), "[" + linkText + "](" + linkUrl + ")");
		}

		// 处理图片
		Pattern imagePattern = Pattern.compile("!\\[(.*?)\\]\\((.*?)\\)");
		Matcher imageMatcher = imagePattern.matcher(line);
		while (imageMatcher.find()) {
			String imageAlt = imageMatcher.group(1);
			String imageUrl = imageMatcher.group(2);
			line = line.replace(imageMatcher.group(), "![" + imageAlt + "](" + imageUrl + ")");
		}
		return line;
	}

	private static String repeat(String str, int count) {
		StringBuilder sb = new StringBuilder();
		for (int i = 0; i < count; i++) {
			sb.append(str);
		}
		return sb.toString();
	}

	private static int detectTitleLevel(String line) {
		if (line.matches("^#.*")) {
			int level = 0;
			while (level < line.length() && line.charAt(level) == '#') {
				level++;
			}
			return level;
		} else if (line.matches("^\\d+\\. .*")) {
			return 1; // 数字编号标题暂视为一级标题
		}
		return 0;
	}

	private static String extractTitleText(String line, int level) {
		if (level > 0 && line.startsWith("#")) {
			return line.substring(level).trim();
		} else if (line.matches("^\\d+\\. .*")) {
			return line.replaceFirst("^\\d+\\. ", "");
		}
		return line;
	}

	private static String detectListItem(String line) {
		if (line.matches("^[0-9]+\\. .*")) {
			return line;
		} else if (line.matches("^[-*] .*")) {
			return line;
		}
		return null;
	}

	private static boolean looksLikeTableLine(String line) {
		return line.contains("|") && line.split("\\|").length > 1;
	}

	private static boolean looksLikeTableSeparator(String line) {
		return line.matches("^[-| :]+$") && line.contains("|");
	}
}