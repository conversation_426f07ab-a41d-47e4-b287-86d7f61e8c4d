package net.qiyuesuo.pdfbox;

import net.sourceforge.tess4j.Tesseract;
import net.sourceforge.tess4j.TesseractException;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.awt.image.BufferedImageOp;
import java.awt.image.ConvolveOp;
import java.awt.image.Kernel;
import java.io.File;
import java.io.IOException;


public class TesseractOcr {
	public static void main(String[] args) {
		Tesseract tesseract = new Tesseract();

		// 扫描件优化配置（效果较好
		tesseract.setPageSegMode(3); // 自动页面分割
		tesseract.setOcrEngineMode(3); // 使用LSTM引擎

		// 照片文件配置
//		tesseract.setPageSegMode(11); // 稀疏文本识别
//		tesseract.setOcrEngineMode(4); // 传统+LSTM混合模式


		try {
//			String imgfilestr = "/Users/<USER>/Downloads/822cc470c961c5343fa297e75df7dcbf.jpeg";
			String imgfilestr = "src/test/resources/pic/签名.png";


			// 设置 Tesseract 数据文件路径
			tesseract.setDatapath("src/main/resources/tessdata");
			// 设置识别语言
			tesseract.setLanguage("eng+chi_sim"); // 支持中英文识别

			// 识别 JPEG 图片
			File imageFile = new File(imgfilestr);
			BufferedImage image = ImageIO.read(imageFile);
//			image = preprocessImage(image); //执行这个方法后，乱字更多了
			String jpegResult = tesseract.doOCR(image);
			System.out.println("JPEG 识别结果：\n" + jpegResult);

			// 可以继续添加其他格式图片的识别代码

		} catch (TesseractException e) {
			System.err.println("OCR 识别出错：" + e.getMessage());
		} catch (IOException e) {
			throw new RuntimeException(e);
		}
	}

	public static void main0(String[] args) throws TesseractException {
		long start = System.currentTimeMillis();
		System.out.println("开始OCR文字识图，请稍后...");
		//加载要识别的图片
		String imgfile = "/Users/<USER>/Downloads/WechatIMG161.jpg";
		java.io.File image = new java.io.File(imgfile);
		//设置配置文件夹微视、识别语言、识别模式
		Tesseract tesseract = new Tesseract();
		tesseract.setDatapath("src/main/resources/tessdata");
		//设置识别语言为中文简体，（如果要设置为英文可改为"eng"）
		tesseract.setLanguage("chi_sim");
		//使用 OSD 进行自动页面分割以进行图像处理
		tesseract.setPageSegMode(1);
		//设置引擎模式是神经网络LSTM引擎
		tesseract.setOcrEngineMode(1);
		//开始识别整张图片中的文字
		String result = tesseract.doOCR(image);
		long time = System.currentTimeMillis()-start;
		System.out.println("识别结束,耗时："+time+" 毫秒，识别结果如下：");
		System.out.println();
		System.out.println(result);
	}

	private static BufferedImage preprocessImage(BufferedImage image) {
		// 转换为灰度图
		BufferedImage grayImage = new BufferedImage(
				image.getWidth(),
				image.getHeight(),
				BufferedImage.TYPE_BYTE_GRAY
		);
		grayImage.getGraphics().drawImage(image, 0, 0, null);

		// 锐化处理
		float[] sharpenMatrix = { 0, -1, 0, -1, 5, -1, 0, -1, 0 };
		BufferedImageOp sharpenFilter = new ConvolveOp(
				new Kernel(3, 3, sharpenMatrix)
		);
		return sharpenFilter.filter(grayImage, null);
	}

}
