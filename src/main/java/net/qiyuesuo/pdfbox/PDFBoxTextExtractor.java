package net.qiyuesuo.pdfbox;
import org.apache.pdfbox.Loader;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;

import java.io.File;
import java.io.IOException;

/**
 * 可正常提取某页文件内容
 */
public class PDFBoxTextExtractor {
	public static void main(String[] args) {
		// 指定要提取文本的 PDF 文件路径
		String filePath = "/Users/<USER>/Downloads/台州银行行政电子印章系统设计说明书.pdf";

		try (PDDocument document = Loader.loadPDF(new File(filePath))) {
			// 检查 PDF 文件是否加密
			if (!document.isEncrypted()) {
				// 创建 PDFTextStripper 对象，用于提取文本
				PDFTextStripper stripper = new PDFTextStripper();

				// 设置提取文本的起始页和结束页（这里提取全量页面）
				stripper.setStartPage(1);
				stripper.setEndPage(1);

//				stripper.setEndPage(document.getNumberOfPages());

				// 提取文本
				String text = stripper.getText(document);

				// 打印提取的文本
				System.out.println(text);
			}
		} catch (IOException e) {
			// 处理文件读取或解析过程中可能出现的异常
			System.err.println("读取 PDF 文件时发生错误: " + e.getMessage());
			e.printStackTrace();
		}
	}
}