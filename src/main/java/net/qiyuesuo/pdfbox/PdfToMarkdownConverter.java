package net.qiyuesuo.pdfbox;

import org.apache.pdfbox.Loader;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class PdfToMarkdownConverter {
	public static void main(String[] args) {
//		String pdfFilePath = "/Users/<USER>/Downloads/二手车检测评估协议.pdf";
		String pdfFilePath = "/Users/<USER>/Downloads/台州银行行政电子印章系统新增人力招聘及入职管理相关功能项目-详细设计说明书.pdf";
		String markdownFilePath = "/Users/<USER>/Downloads/台州银行行政电子印章系统新增人力招聘及入职管理相关功能项目-详细设计说明书.md";
		try {
			FileWriter writer = new FileWriter(markdownFilePath);
			String markdownContent = convertPdfToMarkdown(pdfFilePath);
			writer.write(markdownContent);
			System.out.println(markdownContent);
		} catch (IOException e) {
			e.printStackTrace();
		}
	}

	public static String convertPdfToMarkdown(String pdfFilePath) throws IOException {
		// 读取 PDF 文件内容
		String pdfText = extractTextFromPdf(pdfFilePath);

		StringBuilder markdown = new StringBuilder();
		String[] lines = pdfText.split("\n");
		List<String> tableLines = new ArrayList<>();
		boolean inTable = false;

		for (String line : lines) {
			line = line.trim();

			// 处理标题
			if (line.startsWith("#")) {
				int level = 0;
				while (line.charAt(level) == '#') {
					level++;
				}
				String title = line.substring(level).trim();
				markdown.append(repeat("#", level)).append(" ").append(title).append("\n");
			}
			// 处理列表
			else if (line.matches("^[0-9]+\\.|^- |^\\* ")) {
				markdown.append(line).append("\n");
			}
			// 处理表格
			else if (line.contains("|")) {
				if (!inTable) {
					inTable = true;
				}
				tableLines.add(line);
				if (tableLines.size() > 1 && tableLines.get(tableLines.size() - 1).contains("-")) {
					for (String tableLine : tableLines) {
						markdown.append(tableLine).append("\n");
					}
					tableLines.clear();
				}
			}
			// 处理段落
			else {
				if (inTable) {
					inTable = false;
					if (!tableLines.isEmpty()) {
						for (String tableLine : tableLines) {
							markdown.append(tableLine).append("\n");
						}
						tableLines.clear();
					}
				}
				// 处理链接和图片
				line = processLinksAndImages(line);
				markdown.append(line).append("\n");
			}
		}

		return markdown.toString();
	}

	private static String extractTextFromPdf(String pdfFilePath) throws IOException {
		try (PDDocument document = Loader.loadPDF(new File(pdfFilePath))) {
			PDFTextStripper pdfStripper = new PDFTextStripper();
			return pdfStripper.getText(document);
		}
	}

	private static String processLinksAndImages(String line) {
		// 处理链接
		Pattern linkPattern = Pattern.compile("\\[(.*?)\\]\\((.*?)\\)");
		Matcher linkMatcher = linkPattern.matcher(line);
		while (linkMatcher.find()) {
			String linkText = linkMatcher.group(1);
			String linkUrl = linkMatcher.group(2);
			line = line.replace(linkMatcher.group(), "[" + linkText + "](" + linkUrl + ")");
		}

		// 处理图片
		Pattern imagePattern = Pattern.compile("!\\[(.*?)\\]\\((.*?)\\)");
		Matcher imageMatcher = imagePattern.matcher(line);
		while (imageMatcher.find()) {
			String imageAlt = imageMatcher.group(1);
			String imageUrl = imageMatcher.group(2);
			line = line.replace(imageMatcher.group(), "![" + imageAlt + "](" + imageUrl + ")");
		}
		return line;
	}

	// 自定义的 repeat 方法
	private static String repeat(String str, int count) {
		StringBuilder sb = new StringBuilder();
		for (int i = 0; i < count; i++) {
			sb.append(str);
		}
		return sb.toString();
	}
}
