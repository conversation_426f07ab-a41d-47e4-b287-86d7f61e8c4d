# 背景
给定一份word文件（docx后缀）



目标：

1、提取word文件中**指定页**的文本内容，无需保留格式

2、提取word文件中**指定页**的页眉、页脚中文本内容 ，无需保留格式

# 选型

**第三方框架：**

1. aspose-words 22.12
2. POI 4.1.2
3. Itext 5.5.9



**思路：**

提取指定页的正文、页眉、页脚内容，需要构建该页的对象，包含该页的信息，然后进行内容提取、格式清洗、文本压缩处理。
构建对象方式可以是相关类库提供实现方法直接获取该页的正文对象、页眉页脚。也可以按页分割将word指定页单独分割出来，然后再提取内容。



**写在前面：**

本次测试使用的第三方框架来源于标准版本4.3.5的pom.xml文件下的commo-word和common-pdf依赖。

```
<dependency>
    <groupId>net.qiyuesuo.common</groupId>
    <artifactId>common-pdf</artifactId>
    <version>1.2.0-SNAPSHOT</version>
</dependency>

<dependency>
    <groupId>net.qiyuesuo.common</groupId>
    <artifactId>common-word</artifactId>
    <version>1.3.0-SNAPSHOT</version>
</dependency>
```



本次测试使用的环境版本：

JDK: jdk8_251

Maven: 3.6.3



# 效果

## Aspose-Word

- aspose-word在[页面分割](https://docs.aspose.com/words/java/split-a-document/#splitting-by-pages)，[提取文本](https://docs.aspose.com/words/net/how-to-extract-selected-content-between-nodes-in-a-document/)方面有现成的api，可以方便的进行文本操作：先找到指定页，然后单独分割出来，最后对正文内容、页眉、页脚进行提取。

- 提取的准确性较高，未发现提取字符乱码或丢失的问题。**但是对于序号等不会解析提取**

- 但是当前版本的Aspose-word分割页面后，会导致排版布局有一定范围内的**误差**。Aspose-Word提供在线拆分服务，拆分Word后，排版也存在一定的误差：[Aspose-Word在线拆分word文件地址](https://products.aspose.app/words/zh/splitter/word)

- 对于误差，官网以及论坛有对应的[解释](https://forum.aspose.com/t/pages-extracted-using-extractpages-display-abnormally/260023/5)：

  ![image-20230524100006205](/Users/<USER>/IdeaProjects/QIYUESUO/WordTextExtraction/README.assets/image-20230524100006205.png)

  ![image-20230524104156357](/Users/<USER>/IdeaProjects/QIYUESUO/WordTextExtraction/README.assets/image-20230524104156357.png)

- 用Aspose sdk拆分时一个现象是：拆分的最后一页排版错乱的可能性会比较大。例如拆分单页的那一页，或者拆分多页的最后一页，排版与原文件差异较大。![image-20230524101912180](/Users/<USER>/IdeaProjects/QIYUESUO/WordTextExtraction/README.assets/image-20230524101912180.png)

  

  原因是在调用拆分方法时，[最后一页末尾会被aspose加上分页符](https://forum.aspose.com/t/last-line-of-last-paragrph-getting-disturb-getting-extra-space-at-end/260821)，这个分页符的位置可能与word实际渲染时分页计算的位置不一致。![input](/Users/<USER>/IdeaProjects/QIYUESUO/WordTextExtraction/README.assets/input-4899800.png)

- 由于分割后排版差异的存在，导致Aspose-Word对于页面正文的提取其实并不准确。

- Aspose对于页眉页脚的提取，暂未发现问题

  页眉：

  ![image-20230524184708124](/Users/<USER>/IdeaProjects/QIYUESUO/WordTextExtraction/README.assets/image-20230524184708124.png)

  页脚：

  ![image-20230524184550444](/Users/<USER>/IdeaProjects/QIYUESUO/WordTextExtraction/README.assets/image-20230524184550444.png)

  页面展示：
  ![image-20230524184805972](/Users/<USER>/IdeaProjects/QIYUESUO/WordTextExtraction/README.assets/image-20230524184805972.png)

## POI

- ~~分页提取api~~：POI提供了XWPFWordExtractor对整个word的页眉、正文、页脚进行内容提取，但不支持按照页数分割word。该方案不生效。
- ~~截取分页符号进行页分割~~：手动插入的**硬分页**符号可以获取，但动态计算生成的**软分页**符号无法通过POI获取。但WordprocessingML与POI实际解析出的XWPFDocument对象有所差异，因此无法实现通过分页符分割内容。该方案不生效。
- [~~查询 Word 文档的 DOM 模型，然后确定每页上有多少段落，然后进行分割~~](https://stackoverflow.com/questions/38047893/apache-poi-split-word-document-docx-to-pages)：该方案不生效。
- [其他相关的回答](https://stackoverflow.com/questions/71112279/is-there-a-way-to-keep-track-of-the-current-page-number-within-a-xwpfdocument-a)
- POI在提取指定页正文上暂未能找到对应的解决方案。暂不深入研究。
- POI在提取页眉和页脚方面，提供了[对应的API](https://poi.apache.org/components/document/quick-guide-xwpf.html#:~:text=properties%20from%20that.-,Headers%20and%20Footers,-To%20get%20at)，可根据需要获取首页、默认页数的页眉页脚，从而获取对应的文本。

## IText

- Word为流式文档，在获取指定页面时需要先进行渲染分页，再根据页面获取对应内容。而PDF格式的文档为固定页面文档，因此获取正文内容时，尝试转成pdf处理，再获取正文内容。

- 在word转成pdf后，页眉页脚页会作为pdf文本内容的一部分，无法区分出来，在提取的时候会影响正文内容的准确性。因此在转换之前应该处理页眉页脚。

- 去除页眉页脚会打乱原word排版布局，因此采用文本替换的方式，将页眉页脚的文本统一处理成空格。

  ![image-20230524174144573](/Users/<USER>/IdeaProjects/QIYUESUO/WordTextExtraction/README.assets/image-20230524174144573.png)

- 转成PDF后，文档排版基本一致，本次使用的pdf转换框架为Aspose-word，方法为

  `com.aspose.words.Document#save(java.io.OutputStream, com.aspose.words.SaveOptions)`

  不同的word转pdf类库转换效果可能有所差异，对于上面word文档第三页，Aspose-word转换出的pdf效果如下：

  ![image-20230524174313967](/Users/<USER>/IdeaProjects/QIYUESUO/WordTextExtraction/README.assets/image-20230524174313967.png)

-  对于PDF文本内容的提取，Itext提供了PdfReaderContentParser进行指定页面的文本提取，另外也支持实现TextExtractionStrategy接口自定义文本提取策略。pdf正文展示页面如上图，提取效果如下：

  ![1](/Users/<USER>/IdeaProjects/QIYUESUO/WordTextExtraction/README.assets/1.png)

- 由word转换pdf后，页眉和页脚会作为pdf内容的一部分，无法对转换前的页眉和页脚内容进行区分和提取。



# 实现

具体的实现在`net/qiyuesuo/extraction/extractor/word`包下Extractor结尾的类，具体逻辑在类中的doExtract方法内。

测试用例在`src/test/java/net/qiyuesuo/extraction/service/WordContentExtractServiceImplTest.java` 内

# 总结

本项目粗略的尝试了 提取word指定页文本内容方案的可行性，根据现象做出如下推断和总结：

1. 对于Word指定页的正文提取，无法直接以word的形式进行处理。需要转换成pdf，固定页面和内容，然后进行正文提取，此方案可行。
2. 对于Word指定页的页脚页眉的提取，可以直接以word的形式进行处理，提取出较为准确的文本内容。
3. 在选型方面，正文提取采用Aspose-word + Itext 处理能较为贴切的实现要求。页眉和页脚提取可使用Aspose-word 提供的sdk直接进行提取处理即可。
4. 本次测试仅为探究方案的可行性，仅对resource下的exampl.docx进行了测试。对于不同内容的word文件，或许会存在适用性和兼容性的问题。



# 参考链接

[【Aspose】从 Java 中的 Word DOC 中提取文本](https://blog.aspose.com/zh/words/extract-text-from-word-in-java/#Extract-Text-from-a-Word-Document)

[【Aspose】查找页脚字符串](https://docs.aspose.com/words/java/find-and-replace/#find-and-replace-string-in-header-or-footer-of-a-document)

[软分页符](https://www.computerhope.com/jargon/s/softpage.htm)

[WordProcessingML 文档的结构]()

[【class】PageBreakBefore](https://learn.microsoft.com/en-us/office/vba/api/word.paragraphformat.pagebreakbefore)

[【class】LastRenderedPageBreak](https://learn.microsoft.com/en-us/dotnet/api/documentformat.openxml.wordprocessing.lastrenderedpagebreak?view=openxml-2.8.1)

[【Aspose】使用 Java 删除 Word 文档 (DOCX/DOC) 的页眉和页脚](https://blog.aspose.com/zh/words/add-remove-header-footer-in-word-java/#section3:~:text=%E5%92%8C%E9%A1%B5%E8%84%9A%E3%80%82-,%E4%BD%BF%E7%94%A8%20Java%20%E5%88%A0%E9%99%A4%20Word%20%E6%96%87%E6%A1%A3%20(DOCX/DOC)%20%E7%9A%84%E9%A1%B5%E7%9C%89%E5%92%8C%E9%A1%B5%E8%84%9A,-%23)

[POI-XWPF - 快速指南](https://poi.apache.org/components/document/quick-guide-xwpf.html)

[java使用poi操作word文档(段落、表格、页眉、页脚)](https://blog.csdn.net/a15906449458/article/details/121792056?ops_request_misc=&request_id=&biz_id=102&utm_term=POI%E6%8F%90%E5%8F%96%E9%A1%B5%E7%9C%89&utm_medium=distribute.pc_search_result.none-task-blog-2~all~sobaiduweb~default-2-121792056.142^v87^koosearch_v1,239^v2^insert_chatgpt&spm=1018.2226.3001.4187)

[【Itext】extractpagecontent](https://kb.itextpdf.com/home/<USER>/examples/itext-in-action-chapter-15-page-content-and-structure#iTextinActionChapter15:Pagecontentandstructure-extractpagecontent)