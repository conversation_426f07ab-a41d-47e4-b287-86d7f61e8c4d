package net.qiyuesuo.pdfbox;

import org.apache.pdfbox.Loader;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.apache.pdfbox.pdmodel.common.PDRectangle;
import org.apache.pdfbox.pdmodel.font.PDFont;
import org.apache.pdfbox.pdmodel.font.PDType1Font;
import org.apache.pdfbox.pdmodel.font.Standard14Fonts;
import org.apache.pdfbox.pdmodel.graphics.color.PDColor;
import org.apache.pdfbox.pdmodel.graphics.color.PDDeviceRGB;
import org.apache.pdfbox.text.PDFTextStripperByArea;
import org.apache.pdfbox.text.TextPosition;

import java.awt.*;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Logger;
import java.util.logging.Level;

public class PDFTextReplacer {

    private static final Logger LOGGER = Logger.getLogger(PDFTextReplacer.class.getName());
    private static final String DEFAULT_REPLACEMENT_CHAR = " ";
    private static final PDColor WHITE_COLOR = new PDColor(new float[]{1.0f, 1.0f, 1.0f}, PDDeviceRGB.INSTANCE);

    public static void main(String[] args) {
        String inputFilePath = "/Users/<USER>/Downloads/test111.pdf";
        String outputFilePath = "/Users/<USER>/Downloads/out_test111.pdf";
        String textToReplace = "[[李四-签名]]";

        try {
            replaceTextInPDF(inputFilePath, outputFilePath, textToReplace);
            System.out.println("PDF文本替换完成，输出文件: " + outputFilePath);
        } catch (IOException e) {
            System.err.println("处理PDF时出错: " + e.getMessage());
            LOGGER.log(Level.SEVERE, "PDF处理失败", e);
        }
    }

    /**
     * 创建重复字符串的辅助方法
     * @param str 要重复的字符串
     * @param count 重复次数
     * @return 重复后的字符串
     */
    private static String createRepeatedString(String str, int count) {
        if (count <= 0) {
            return "";
        }
        if (str == null || str.isEmpty()) {
            return "";
        }
        // 使用String.repeat()方法（Java 11+）或StringBuilder
        if (str.length() == 1) {
            return str.repeat(count);
        }

        // 预计算总长度以提高效率
        int totalLength = str.length() * count;
        StringBuilder sb = new StringBuilder(totalLength);
        for (int i = 0; i < count; i++) {
            sb.append(str);
        }
        return sb.toString();
    }

    /**
     * 替换PDF中的指定文本为等长的空白
     * @param inputFilePath 输入PDF文件路径
     * @param outputFilePath 输出PDF文件路径
     * @param textToReplace 要替换的文本
     * @throws IOException 如果处理PDF时发生错误
     */
    public static void replaceTextInPDF(String inputFilePath, String outputFilePath, String textToReplace) 
            throws IOException {
        // 加载PDF文档
        try (PDDocument document = Loader.loadPDF(new File(inputFilePath))) {
            
            // 遍历每一页
            for (PDPage page : document.getPages()) {
                // 创建自定义的文本剥离器来获取文本位置
                CustomTextStripper stripper = new CustomTextStripper();
                PDRectangle cropBox = page.getCropBox();
                
                // 设置要处理的区域为整个页面
                Rectangle rect = new Rectangle(
                    (int) cropBox.getLowerLeftX(),
                    (int) cropBox.getLowerLeftY(),
                    (int) cropBox.getWidth(),
                    (int) cropBox.getHeight()
                );
                stripper.addRegion("page", rect);
                stripper.extractRegions(page);
                
                // 获取找到的所有匹配文本位置
                List<TextPosition> matches = stripper.findTextPositions(textToReplace);
                
                // 如果找到匹配项，创建内容流覆盖原有文本
                if (!matches.isEmpty()) {
                    try (PDPageContentStream contentStream = new PDPageContentStream(
                            document, page, PDPageContentStream.AppendMode.APPEND, true)) {
                        
                        // 对于每个匹配项，用空白覆盖
                        for (TextPosition match : matches) {
                            // 获取文本位置和字体信息
                            float x = match.getXDirAdj();
                            float y = match.getYDirAdj();
                            float fontSize = match.getFontSize();
                            
                            // 创建与原文字符等长的空白字符串
                            String replacement = createRepeatedString(" ", textToReplace.length());

                            PDFont font = new PDType1Font(Standard14Fonts.FontName.HELVETICA);
                            contentStream.setFont(font, fontSize);

                            contentStream.beginText();
                            contentStream.newLineAtOffset(x, y);
                            contentStream.showText(replacement);
                            contentStream.endText();
                        }
                    }
                }
            }
            
            // 保存修改后的PDF
            document.save(outputFilePath);
        }

    }

    /**
     * 自定义文本剥离器，用于获取文本位置信息
     */
    static class CustomTextStripper extends PDFTextStripperByArea {
        List<TextPosition> result = new ArrayList<>();
        List<TextPosition> allTextPositions = new ArrayList<>();

        public CustomTextStripper() throws IOException {
            super();
        }
        
        /**
         * 查找指定文本的位置
         * @param text 要查找的文本
         * @return 找到的文本位置列表
         */
        public List<TextPosition> findTextPositions(String text) {
            // 收集所有文本位置
            try {
                // 提取当前页的所有文本
                String pageText = getTextForRegion("page");
                int textLength = text.length();

                // 遍历所有文本位置，查找匹配的文本序列
                int startIndex = 0;
                while (startIndex <= pageText.length() - textLength) {
                    int index = pageText.indexOf(text, startIndex);
                    if (index == -1) {
                        break;
                    }

                    // 找到匹配文本，收集对应的位置信息
                    if (index + textLength <= allTextPositions.size()) {
                        TextPosition firstChar = allTextPositions.get(index);
                        result.add(firstChar);
                    }

                    startIndex = index + textLength;
                }
            } catch (Exception e) {
                e.printStackTrace();
            }

            return result;
        }

        @Override
        protected void writeString(String string, List<TextPosition> textPositions) throws IOException {
            // 收集所有文本位置信息
            allTextPositions.addAll(textPositions);
            super.writeString(string, textPositions);
        }
    }
}
