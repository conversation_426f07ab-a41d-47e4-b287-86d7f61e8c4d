package net.qiyuesuo.pdfbox;

import org.apache.pdfbox.Loader;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.apache.pdfbox.pdmodel.common.PDRectangle;
import org.apache.pdfbox.pdmodel.font.PDFont;
import org.apache.pdfbox.pdmodel.font.PDType1Font;
import org.apache.pdfbox.pdmodel.font.Standard14Fonts;
import org.apache.pdfbox.pdmodel.graphics.color.PDColor;
import org.apache.pdfbox.pdmodel.graphics.color.PDDeviceRGB;
import org.apache.pdfbox.text.PDFTextStripperByArea;
import org.apache.pdfbox.text.TextPosition;

import java.awt.*;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Logger;
import java.util.logging.Level;

public class PDFTextReplacer {

    private static final Logger LOGGER = Logger.getLogger(PDFTextReplacer.class.getName());
    private static final String DEFAULT_REPLACEMENT_CHAR = " ";
    private static final PDColor WHITE_COLOR = new PDColor(new float[]{1.0f, 1.0f, 1.0f}, PDDeviceRGB.INSTANCE);

    public static void main(String[] args) {
        String inputFilePath = "/Users/<USER>/Downloads/test111.pdf";
        String outputFilePath = "/Users/<USER>/Downloads/out_test111.pdf";
        String textToReplace = "[[李四-签名]]";

        try {
            replaceTextInPDF(inputFilePath, outputFilePath, textToReplace);
            System.out.println("PDF文本替换完成，输出文件: " + outputFilePath);
        } catch (IOException e) {
            System.err.println("处理PDF时出错: " + e.getMessage());
            LOGGER.log(Level.SEVERE, "PDF处理失败", e);
        }
    }

    /**
     * 创建重复字符串的辅助方法
     * @param str 要重复的字符串
     * @param count 重复次数
     * @return 重复后的字符串
     */
    private static String createRepeatedString(String str, int count) {
        if (count <= 0) {
            return "";
        }
        if (str == null || str.isEmpty()) {
            return "";
        }
        // 使用String.repeat()方法（Java 11+）或StringBuilder
        if (str.length() == 1) {
            return str.repeat(count);
        }

        // 预计算总长度以提高效率
        int totalLength = str.length() * count;
        StringBuilder sb = new StringBuilder(totalLength);
        for (int i = 0; i < count; i++) {
            sb.append(str);
        }
        return sb.toString();
    }

    /**
     * 替换PDF中的指定文本为等长的空白
     * @param inputFilePath 输入PDF文件路径
     * @param outputFilePath 输出PDF文件路径
     * @param textToReplace 要替换的文本
     * @throws IOException 如果处理PDF时发生错误
     */
    public static void replaceTextInPDF(String inputFilePath, String outputFilePath, String textToReplace)
            throws IOException {
        replaceTextInPDF(inputFilePath, outputFilePath, textToReplace, DEFAULT_REPLACEMENT_CHAR);
    }

    /**
     * 替换PDF中的指定文本为指定的替换字符
     * @param inputFilePath 输入PDF文件路径
     * @param outputFilePath 输出PDF文件路径
     * @param textToReplace 要替换的文本
     * @param replacementChar 替换字符
     * @throws IOException 如果处理PDF时发生错误
     */
    public static void replaceTextInPDF(String inputFilePath, String outputFilePath,
                                      String textToReplace, String replacementChar) throws IOException {
        if (inputFilePath == null || outputFilePath == null || textToReplace == null) {
            throw new IllegalArgumentException("输入参数不能为null");
        }

        File inputFile = new File(inputFilePath);
        if (!inputFile.exists()) {
            throw new IOException("输入文件不存在: " + inputFilePath);
        }

        LOGGER.info("开始处理PDF文件: " + inputFilePath);

        // 加载PDF文档
        try (PDDocument document = Loader.loadPDF(inputFile)) {
            int totalMatches = 0;

            // 遍历每一页
            for (int pageIndex = 0; pageIndex < document.getNumberOfPages(); pageIndex++) {
                PDPage page = document.getPage(pageIndex);
                int pageMatches = replaceTextOnPage(document, page, textToReplace, replacementChar);
                totalMatches += pageMatches;

                if (pageMatches > 0) {
                    LOGGER.info("第" + (pageIndex + 1) + "页找到并替换了" + pageMatches + "个匹配项");
                }
            }

            // 保存修改后的PDF
            document.save(outputFilePath);
            LOGGER.info("PDF处理完成，共替换了" + totalMatches + "个匹配项，输出文件: " + outputFilePath);
        }
    }

    /**
     * 在单个页面上替换文本
     * @param document PDF文档
     * @param page 页面
     * @param textToReplace 要替换的文本
     * @param replacementChar 替换字符
     * @return 替换的匹配项数量
     * @throws IOException 如果处理过程中发生错误
     */
    private static int replaceTextOnPage(PDDocument document, PDPage page,
                                       String textToReplace, String replacementChar) throws IOException {
        // 创建自定义的文本剥离器来获取文本位置
        CustomTextStripper stripper = new CustomTextStripper();
        PDRectangle cropBox = page.getCropBox();

        // 设置要处理的区域为整个页面
        Rectangle rect = new Rectangle(
            (int) cropBox.getLowerLeftX(),
            (int) cropBox.getLowerLeftY(),
            (int) cropBox.getWidth(),
            (int) cropBox.getHeight()
        );
        stripper.addRegion("page", rect);
        stripper.extractRegions(page);

        // 获取找到的所有匹配文本位置
        List<TextPosition> matches = stripper.findTextPositions(textToReplace);

        // 如果找到匹配项，创建内容流覆盖原有文本
        if (!matches.isEmpty()) {
            try (PDPageContentStream contentStream = new PDPageContentStream(
                    document, page, PDPageContentStream.AppendMode.APPEND, true)) {

                // 设置白色填充色用于覆盖
                contentStream.setNonStrokingColor(WHITE_COLOR);

                // 对于每个匹配项，用指定字符覆盖
                for (TextPosition match : matches) {
                    replaceTextAtPosition(contentStream, match, textToReplace, replacementChar);
                }
            }
        }

        return matches.size();
    }

    /**
     * 在指定位置替换文本
     * @param contentStream 内容流
     * @param match 文本位置
     * @param textToReplace 要替换的文本
     * @param replacementChar 替换字符
     * @throws IOException 如果处理过程中发生错误
     */
    private static void replaceTextAtPosition(PDPageContentStream contentStream, TextPosition match,
                                            String textToReplace, String replacementChar) throws IOException {
        // 获取文本位置和字体信息
        float x = match.getXDirAdj();
        float y = match.getYDirAdj();
        float fontSize = match.getFontSize();
        float width = match.getWidthDirAdj();
        float height = match.getHeightDir();

        // 先绘制白色矩形覆盖原文本
        contentStream.addRect(x, y - height * 0.2f, width, height);
        contentStream.fill();

        // 如果需要显示替换字符（非空白覆盖）
        if (replacementChar != null && !replacementChar.trim().isEmpty()) {
            // 创建与原文字符等长的替换字符串
            String replacement = createRepeatedString(replacementChar, textToReplace.length());

            PDFont font = new PDType1Font(Standard14Fonts.FontName.HELVETICA);
            contentStream.setFont(font, fontSize);
            contentStream.setNonStrokingColor(0, 0, 0); // 黑色文字

            contentStream.beginText();
            contentStream.newLineAtOffset(x, y);
            contentStream.showText(replacement);
            contentStream.endText();
        }
    }

    /**
     * 自定义文本剥离器，用于获取文本位置信息
     */
    static class CustomTextStripper extends PDFTextStripperByArea {
        private final List<TextPosition> result = new ArrayList<>();
        private final List<TextPosition> allTextPositions = new ArrayList<>();
        private final StringBuilder textBuilder = new StringBuilder();

        public CustomTextStripper() throws IOException {
            super();
            setSortByPosition(true);
        }

        /**
         * 查找指定文本的位置
         * @param text 要查找的文本
         * @return 找到的文本位置列表
         */
        public List<TextPosition> findTextPositions(String text) {
            result.clear(); // 清除之前的结果

            if (text == null || text.isEmpty()) {
                return result;
            }

            try {
                // 提取当前页的所有文本
                String pageText = getTextForRegion("page");
                if (pageText == null || pageText.isEmpty()) {
                    return result;
                }

                int textLength = text.length();

                // 遍历所有文本位置，查找匹配的文本序列
                int startIndex = 0;
                while (startIndex <= pageText.length() - textLength) {
                    int index = pageText.indexOf(text, startIndex);
                    if (index == -1) {
                        break;
                    }

                    // 找到匹配文本，收集对应的位置信息
                    if (index < allTextPositions.size()) {
                        TextPosition firstChar = allTextPositions.get(index);
                        result.add(firstChar);
                        LOGGER.fine("找到匹配文本 '" + text + "' 在位置: (" +
                                   firstChar.getXDirAdj() + ", " + firstChar.getYDirAdj() + ")");
                    }

                    startIndex = index + 1; // 改为+1以支持重叠匹配
                }
            } catch (Exception e) {
                LOGGER.log(Level.WARNING, "查找文本位置时发生错误", e);
            }

            return new ArrayList<>(result); // 返回副本以避免外部修改
        }

        @Override
        protected void writeString(String string, List<TextPosition> textPositions) throws IOException {
            // 收集所有文本位置信息
            if (textPositions != null && !textPositions.isEmpty()) {
                allTextPositions.addAll(textPositions);
                textBuilder.append(string);
            }
            super.writeString(string, textPositions);
        }

        /**
         * 重置内部状态，用于处理新页面
         */
        public void reset() {
            result.clear();
            allTextPositions.clear();
            textBuilder.setLength(0);
        }
    }
}
